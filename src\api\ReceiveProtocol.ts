/*接收协议处理*/

import StringView from '../../static/js/stringview';

// 定义全局变量
let public_buffer: number[] = [];
let count: number = 0; // 次数
let public_length: number = 0; // 总数据长度
let datastr: string = '';

/**
 * 处理接收到的数据
 * @param receiveData - 接收到的二进制数据
 * @returns 解析后的JSON对象，如果解析未完成则返回undefined
 */
export function receiveProtocol(receiveData: ArrayBuffer): any {
  const receive = new StringView(receiveData);
  const recBuffer: Uint8Array = receive.rawData as Uint8Array; // 收到的数据
  let returnData: any;

  // 第一次传数据，但数据不足以包含头部信息
  if (count === 0 && recBuffer.length < 6) return;

  // 第一次数据里面包含的数据长度
  if (count === 0) {
    const lenBuffer = recBuffer.subarray(2, 6); // 第一包接收到的数据长度
    public_length = (lenBuffer[0] & 0xFF) | 
                   ((lenBuffer[1] & 0xFF) << 8) | 
                   ((lenBuffer[2] & 0xFF) << 16) | 
                   ((lenBuffer[3] & 0xFF) << 24); // 总长度
  }

  // 数据一次性发完
  if (recBuffer.length - 6 === public_length) {
    const dataBuffer = recBuffer.subarray(6, 6 + public_length);
    const str = new StringView(dataBuffer).toString();
    try {
      returnData = JSON.parse(str);
    } catch (e) {
      console.error('JSON解析错误:', e);
      return undefined;
    }
  }

  // 多次数据拼接
  if (recBuffer.length - 6 !== public_length) {
    count = 1; // 次数加1

    public_buffer = public_buffer.concat(Array.from(new Uint8Array(recBuffer)));

    const arr = new Uint8Array(public_buffer);
    
    if (arr.length - 6 === public_length) {
      const dataBuffer = arr.subarray(6, 6 + public_length);
      datastr = new StringView(dataBuffer).toString();
      
      try {
        returnData = JSON.parse(datastr);
      } catch (e) {
        console.error('JSON解析错误:', e);
        return undefined;
      }
      
      // 恢复初始化,方便下一次接收数据
      count = 0;
      public_buffer = [];
      datastr = '';
    }
  }

  return returnData;
}