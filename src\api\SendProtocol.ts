/* 发送协议组装 */
import StringView from '../../static/js/stringview';

/**
 * 包装协议，包装发送数据
 * @param sendData - 发送的数据
 * @param type - 类型 1 服务器处理，2 透传给 RCU 处理
 */
export function PackProtocol(sendData: string, type: number): Uint8Array {
  // 不加密
  // return sendData;
  // 数据加密
  const dataBuffer = new StringView.StringView(sendData)

  // 数据长度
  const dataLen = dataBuffer.rawData.length;
  const lenBuffer = new Uint8Array(4);

  // 0XFF 即 255, 1111 1111
  lenBuffer[0] = dataLen & 0xFF; // 取低八位写入高地址中
  lenBuffer[1] = (dataLen >> 8) & 0xFF;
  lenBuffer[2] = (dataLen >> 16) & 0xFF;
  lenBuffer[3] = (dataLen >> 24) & 0xFF;

  // 创建初始化为 0 的，包含 2 个元素的无符号整型数组
  const typeBuffer = new Uint8Array(2)
  typeBuffer[0] = type & 0xff
  typeBuffer[1] = (type >> 8) & 0xff

  const Buffer = new Uint8Array(6 + dataLen)

  Buffer.set(typeBuffer, 0)
  Buffer.set(lenBuffer, 2);
  Buffer.set(dataBuffer.rawData, 6);

  return Buffer;
}