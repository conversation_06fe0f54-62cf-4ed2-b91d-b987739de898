
import { PackProtocol } from './SendProtocol'
const SERVER_TAG = 1


// 获取系统信息
export function getSysInfo() {
        var params = {
                method: 'get_host_info',
                seq: 1
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 修改(同步)系统时间
export function updateSystemTime(info) {

        var params = {
                method: 'set_systime',
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 获取验证码
export function getVerifyCode() {
        var paprams = {
                method: "get_verify_code",
                seq: 7,
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}




// 场景控制
export function sceneCtrl(info) {
        var paprams = {
                method: "scene_ctrl",
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}





// 获取主机设备能力列表
export function getHostObjectList(info) {
        var params = {
                method: 'get_host_object_list',
                seq: 16,
                info: info
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)

}

// 设备控制
export function devctrl(info) {
        // info为对象 {vid:vid,attr:{status:type}}
        var paprams = {
                method: "dev_ctrl",
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 设备状态同步
export function syncStatus() {
        var paprams = {
                method: 'sync_status'
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}




// 获取主机网络
export function getNetworkConfig() {
        var paprams = {
                method: 'get_host_network',
                seq: 8,
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}
// 设置主机网络配置
export function setNetworkConfig(info) {
        var paprams = {
                method: 'set_host_network',
                seq: 9,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}


// 导入工程
export function hostImportProj(info) {
        var paprams = {
                method: 'host_import_proj',
                seq: 13,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}
// 导出工程
export function hostExportProj() {
        var paprams = {
                method: 'host_export_proj',
                seq: 12,
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}



// 协处理器升级
export function upgradeCoprocessor(info) {
        var paprams = {
                method: 'coprocessor_upgrade_confirm',
                seq: 14,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}
// 主机升级
export function upgradeHost(info) {
        var paprams = {
                method: 'host_upgrade_confirm',
                seq: 15,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}


// 部署
export function deploy() {
        var params = {
                method: "deploy",
                seq: 3
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)

}

// 重启主机
export function reboot() {
        var params = {
                method: "reboot",
                seq: 16
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 恢复出厂设置
export function restore() {
        var params = {
                method: "restore_factory_setting",
                seq: 17
        }
        const paramStr = JSON.stringify(params)
        return PackProtocol(paramStr, SERVER_TAG)
}



// 获取安防模式
export function getGuardMode() {
        var paprams = {
                method: 'get_guard_mode',
                seq: 15
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 设置布防撤防
export function setGuardMode(info) {
        var paprams = {
                method: 'set_guard_mode',
                seq: 15,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 安防模式状态上报
export function guardModeNotify(info) {
        var paprams = {
                method: 'guard_mode_notify',
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}






// 获取服务器地址和房间号
export function getServerConfig() {
        var paprams = {
                method: "get_host_server_config",
                seq: 9
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}
// 设置服务器地址和房间号

export function setServerConfig(info) {
        var paprams = {
                method: "set_host_server_config",
                seq: 9,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

//读取配置
export function duqu(info, seq) {
        var paprams = {
                method: "read_config",
                seq: seq ? seq : 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}
//下发配置
export function xiafa(info) {
        var paprams = {
                method: "set_config",
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

//搜索网关下的模块
export function searchModule(info) {
        var paprams = {
                method: "sebus_manager_search",
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

export function setSebusBusid(info) {
        var paprams = {
                method: "sebus_manager_set_busid",
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

export function showMe(info) {
        var paprams = {
                method: "sebus_manager_showme",
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

//网关搜索

export function getGatewaySearch() {
        var paprams = {
                method: "search_gateway",
                seq: 2,
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

//修改网关网络信息修改接口
export function updateGatewayNetwork(info) {
        var paprams = {
                method: "set_gateway_network_cfg",
                seq: 2,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}


export function setHomekitConfig() {
        var paprams = {
                method: 'apply_hk_config',
                seq: 2
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

//AI场景

//获取AI支持能力
export function getIAIlist() {
        var paprams = {
                method: 'get_support',
                seq: 1
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 自动生成场景
export function createAIscene(info) {
        var paprams = {
                method: 'create',
                seq: 0,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 用户选择处理
export function stepSelect(info) {
        var paprams = {
                method: 'choice',
                seq: 111,
                info: info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// homekit设备和场景配置下发
export function setApplyHkConfig() {
        var paprams = {
                method: 'apply_hk_config',
                seq: 112
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 获取涂鸦网关在线设备列表接口  get_tuya_devlist
export function getTuyaDevList(info) {
        var paprams = {
                method: 'get_tuya_devlist',
                seq: 113,
                info
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}

// 获取knx网关调试信息接口get_knx_debug_info
export function getKnxDebugInfo() {
        var paprams = {
                method: 'get_knx_debug_info',
                seq: 114,
        }
        const paramStr = JSON.stringify(paprams)
        return PackProtocol(paramStr, SERVER_TAG)
}





