// stores/useRcuStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 定义状态类型
interface DeviceStatus {
  [key: string]: any;
}

interface GatewayConfig {
  [key: string]: any;
}

interface AiSceneStep {
  [key: string]: any;
}

interface ZigBeeDevice {
  [key: string]: any;
}

interface KnxDebugInfo {
  [key: string]: any;
}

interface RcuState {
  systemInfo: any;
  verifyCode: string;
  logList: string[];
  logStop: boolean;
  networkConfig: any;
  serverConfig: any;
  isLoading: boolean;
  guard_mode: string;
  device_status: DeviceStatus;
  sebusdebugFun: Function | null;
  gatewaySearch: any[];
  currentGatewayConfig: GatewayConfig;
  aiSceneList: any[];
  aiStepList: AiSceneStep[];
  aiChoice: any[];
  aiComplete: number;
  zigBeeMac: ZigBeeDevice[];
  zigBeeGatewayConfig: any;
  knxDebugInfo: KnxDebugInfo[];
}

export const useRcuStore = defineStore('rcu', {
  state: (): RcuState => ({
    systemInfo: {},
    verifyCode: '',
    logList: [],
    logStop: false,
    networkConfig: {},
    serverConfig: {},
    isLoading: false,
    guard_mode: '',
    device_status: {},
    sebusdebugFun: null,
    gatewaySearch: [],
    currentGatewayConfig: {},
    aiSceneList: [],
    aiStepList: [],
    aiChoice: [],
    aiComplete: 1,
    zigBeeMac: [],
    zigBeeGatewayConfig: {},
    knxDebugInfo: []
  }),

  actions: {
    // 设置系统信息
    setSystemInfo(val: any) {
      this.systemInfo = val
    },

    // 设置验证码
    setVerifyCode(val: string) {
      console.log(val, '验证码')
      this.verifyCode = val
    },

    // 添加日志
    addLog(val: string) {
      if (this.logStop) return

      if (this.logList.length > 1000) {
        this.logList.shift()
        this.logList.push(val)
      } else {
        this.logList.push(val)
      }
    },

    // 清空日志
    clearLog() {
      this.logList = []
    },

    // 控制是否接收日志
    setLogStop(val: boolean) {
      this.logStop = val
    },

    // 设置网关搜索结果
    addGatewaySearch(val: any) {
      this.gatewaySearch.push(val)
    },

    // 清空网关搜索
    clearGatewaySearch() {
      this.gatewaySearch = []
    },

    // 设置当前网关配置
    setCurrentGatewayConfig(val: GatewayConfig) {
      this.currentGatewayConfig = val
    },

    // 设置主机网络配置
    setNetworkConfig(val: any) {
      this.networkConfig = val;
    },

    // 设置服务器配置
    setServerConfig(val: any) {
      this.serverConfig = val
    },

    // 设置设备状态
    setDeviceStatus(val: DeviceStatus) {
      this.device_status = val
    },

    // 设置安防模式
    setGuardMode(val: string) {
      this.guard_mode = val
    },

    // 设置加载状态
    setIsLoading(val: boolean) {
      this.isLoading = val;
    },

    // 设置调试函数
    setSebusdebugCallback(callback: Function | null) {
      this.sebusdebugFun = callback
    },

    // 调用调试函数
    invokeSebusdebugCallback(value: any) {
      if (typeof this.sebusdebugFun === 'function') {
        this.sebusdebugFun(value)
      }
    },

    // AI 场景设置
    setAiSceneList(val: any[]) {
      this.aiSceneList = val;
      this.aiComplete = 1;
    },
    setAiSceneStep(val: AiSceneStep[]) {
      this.aiStepList = val;
    },
    setAiSceneChoice(val: any[]) {
      this.aiChoice = val;
    },
    incrementAiSceneComplete() {
      this.aiComplete += 1;
    },

    // ZigBee 配置
    setZigBeeMac(val: any) {
      this.zigBeeMac = val?.dev_list || [];
    },
    setZigBeeGatewayConfig(val: any) {
      this.zigBeeGatewayConfig = val;
    },

    // KNX 调试信息
    setKnxDebugInfo(val: any) {
      this.knxDebugInfo = val;
    },
  },
});