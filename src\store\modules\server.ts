import { defineStore } from 'pinia';

// 定义回调函数类型
type CallbackFunction = (value: any) => void;

// 定义 WebSocket 状态类型
type WebSocketState = 0 | 1 | 2;

// 定义 Store 状态接口
interface ServerState {
  wsOnOpen: CallbackFunction | string;    // ws连接成功函数
  wsOnClose: CallbackFunction | string;   // ws连接关闭函数
  wsOnErrot: CallbackFunction | string;   // ws连接错误函数
  wsMessage: CallbackFunction | string;   // ws消息函数
  wsState: WebSocketState;                // ws连接状态 0未连接 1连接成功 2连接错误
  isDeploy: boolean;                      // 是否部署
  callbackfun: CallbackFunction | string; // 设置登录回调
}

// 检查是否为函数的工具函数
const isFunction = (fn: any): fn is CallbackFunction => {
  return Object.prototype.toString.call(fn) === '[object Function]';
};

export const useServerStore = defineStore('server', {
  state: (): ServerState => ({
    wsOnOpen: '',
    wsOnClose: '',
    wsOnErrot: '',
    wsMessage: '',
    wsState: 0,
    isDeploy: false,
    callbackfun: ''
  }),

  getters: {
    // 可以在这里添加需要的 getters
  },

  actions: {
    // 接收到登录返回信息后
    setCallbackValue(val: any) {
      if (isFunction(this.callbackfun)) {
        this.callbackfun(val);
      }
    },

    setWsState(val: WebSocketState) {
      this.wsState = val;
    },

    setDeploy(val: boolean) {
      this.isDeploy = val;
    },

    setWsOnOpenFun(fun: CallbackFunction) {
      this.wsOnOpen = fun;
    },

    setWsOnOpenFunValue(value: any) {
      if (isFunction(this.wsOnOpen)) {
        this.wsOnOpen(value);
      }
    },

    setWsOnCloseFun(fun: CallbackFunction) {
      this.wsOnClose = fun;
    },

    setWsOnCloseFunValue(value: any) {
      if (isFunction(this.wsOnClose)) {
        this.wsOnClose(value);
      }
    },

    setWsOnErrorFun(fun: CallbackFunction) {
      this.wsOnErrot = fun;
    },

    setWsOnErrorFunValue(value: any) {
      if (isFunction(this.wsOnErrot)) {
        this.wsOnErrot(value);
      }
    },

    setWsMessageFun(fun: CallbackFunction) {
      this.wsMessage = fun;
    },

    setWsMessageFunValue(value: any) {
      if (isFunction(this.wsMessage)) {
        this.wsMessage(value);
      }
    }
  }
});