import { defineStore } from 'pinia';
import { useRcuStore } from './rcu';
import { useServerStore } from './server';
import {
  getSysInfo,
  getGuardMode,
  getServerConfig,
} from '@/api/ServiceManage';
// import PUBLIC_URL from '../http/port';
import { receiveProtocol } from '@/api/ReceiveProtocol';
import { ElMessage, ElNotification } from 'element-plus';

// 定义WebSocket连接状态类型
type ConnectionStatus = 'connecting' | 'connected' | 'disconnected';

// 定义自定义WebSocket接口，添加额外属性
interface CustomWebSocket extends WebSocket {
  url: string;
  reconnectAttempts?: number;
  heartbeatTimer?: number;
}

// 定义WebSocket状态接口
interface WebSocketState {
  connections: Record<string, WebSocket>;
  status: Record<string, ConnectionStatus>;
  messageQueue: Record<string, any[]>;
  unacknowledgedMessages: Record<string, any[]>;
}

export const useWebsocketStore = defineStore('websocket', {
  state: (): WebSocketState => ({
    connections: {}, // 存储所有WebSocket实例 { id: WebSocket实例 }
    status: {},      // 存储连接状态 { id: 'connected' | 'disconnected' }
    messageQueue: {}, // 存储未发送的消息队列 { id: [消息数组] }
    unacknowledgedMessages: {} // 存储未确认的消息 { id: [消息数组] }
  }),
  
  actions: {
    // 初始化WebSocket连接
    initConnection({ id, url }: { id: string, url: string }) {
      console.log('初始化WebSocket连接:', id, url)
      const ws = new WebSocket(url) as CustomWebSocket;
   
      
      const rcuStore = useRcuStore();
      const serverStore = useServerStore();

      // 绑定事件
      ws.onopen = () => {
        console.log(`✅连接了 ${id}: ${url}`);
        this.updateStatus({ id, status: 'connected' });
        this.sendQueuedMessages(id); // 发送队列中的消息
        // this.startHeartbeat(id); // 启动心跳
      };
      
      ws.onclose = () => {
        console.log(`❌断开了${id}:${url}`);
        this.updateStatus({ id, status: 'disconnected' });
        this.reconnect(id); // 触发重连
      };
      
      // 消息监听逻辑
      ws.onmessage = (event: MessageEvent) => {
        const reader = new FileReader();
        reader.addEventListener("loadend", () => {
          const result = reader.result as ArrayBuffer;
          const res = receiveProtocol(result);
          if (!res) return;

          console.log(`👌${id}回消息了:`, res);

          if (res.errcode === 0) {
            this.removeUnacknowledgedMessage({ id });
            
            // 获取系统信息
            if (res.method === 'get_host_info_rsp') {
              rcuStore.setSystemInfo(res.info);
            }
            
            if (res.method === 'scene_ctrl_rsp') {
              ElMessage({
                message: '执行成功',
                type: 'success',
                showClose: true
              });
            }

            // 获取验证码
            if (res.method === 'get_verify_code_rsp' || res.method === 'report_verify_code') {
              console.log(res.info.code);
              rcuStore.setVerifyCode(res.info.code);
            }

            // 修改系统时间(同步),重新获取系统时间
            if (res.method === 'set_systime_rsp') {
              this.sendMessage({ id: 'socket', data: getSysInfo() });
            }

            // 部署成功
            if (res.method === 'deploy_rsp') {
              ElMessage({
                message: '正在部署',
                type: 'success',
                showClose: true
              });
            }

            // 获取服务器地址和房间号
            if (res.method === 'get_host_server_config_rsp') {
              rcuStore.setServerConfig(res.info);
            }

            // 设置主机信息
            if (res.method === 'set_host_server_config_rsp') {
              ElMessage({
                message: '操作成功',
                type: 'success',
                showClose: true
              });
              this.sendMessage({ id: 'socket', data: getServerConfig() });
            }

            // 获取主机网络配置
            if (res.method === 'get_host_network_rsp') {
              rcuStore.setNetworkConfig(res.info);
            }

            // 设置主机网络位置
            if (res.method === 'set_host_network_rsp') {
              ElMessage({
                message: '主机网络位置修改成功',
                type: 'success',
                showClose: true
              });
            }

            // 导出工程文件
            // if (res.method === 'host_export_proj_rsp') {
            //   ElNotification({
            //     title: "导出工程文件地址:",
            //     dangerouslyUseHTMLString: true,
            //     message: `<a target='_blank' href=${PUBLIC_URL.HTTP_URL}${res.info.url_path}>${PUBLIC_URL.HTTP_URL}${res.info.url_path}</a>`
            //   });
            // }

            // 导入工程文件
            if (res.method === 'host_import_proj_rsp') {
              ElMessage({
                message: '文件上传成功',
                type: 'success',
                showClose: true
              });
            }

            if (res.method === 'coprocessor_upgrade_confirm_rsp') {
              ElMessage({
                message: '升级完成',
                type: 'success',
                showClose: true
              });
              rcuStore.setIsLoading(false);
            }

            // 固件升级
            if (res.method === 'host_upgrade_confirm_rsp') {
              ElMessage({
                message: '正在升级中',
                type: 'success',
                showClose: true
              });
            }

            // 获取安防状态
            if (res.method === 'get_guard_mode_rsp') {
              rcuStore.setGuardMode(res.info.mode);
            }

            // 安防状态设置
            if (res.method === 'set_guard_mode_rsp') {
              this.sendMessage({ id: 'socket', data: getGuardMode() });
            }

            if (res.method === 'sebus_manager_search_rsp') {
              rcuStore.setSebusdebugCallback(res.info);
            }

            // 重启主机
            if (res.method === 'reboot_rsp') { }

            // 恢复出厂设置
            if (res.method === 'restore_factory_setting_rsp') { }

            // 读取网关配置
            if (res.method === 'read_config_rsp') {
              if (res.seq === 2) {
                rcuStore.setCurrentGatewayConfig(res.info);
              } else {
                rcuStore.setZigBeeGatewayConfig(res.info);
              }
            }

            // 调试-zigbeeMac配置
            if (res.method === 'get_tuya_devlist_rsp') {
              rcuStore.setZigBeeMac(res.info);
            }
            
            // 获取knx网关调试信息接口
            if (res.method === 'get_knx_debug_info_rsp') {
              const arr = [res.info];
              rcuStore.setKnxDebugInfo(arr);
            }

            // AI场景
            if (res.method === 'get_support_rsp') {
              rcuStore.setAiSceneList(res.info);
            }

            if (res.method === 'step_rsp') {
              rcuStore.setAiSceneStep(res.info);
            }

            if (res.method === 'choice_rsp') {
              rcuStore.setAiSceneChoice(res.info);
            }

            if (res.method === 'complete') {
              ElMessage({
                message: '创建完成',
                type: 'success',
                showClose: true
              });
              rcuStore.setAiSceneChoice(res);
            }

            if (res.method === 'apply_hk_config_rsp') {
              ElMessage({
                message: '配置成功,配置生效需要1-2分钟',
                type: 'success',
                showClose: true
              });
            }
          } else if (res.errcode === 1) {
            if (res.method === 'scene_ctrl_rsp') {
              ElMessage({
                message: '执行失败',
                type: 'error',
                showClose: true
              });
            }
            
            ElMessage({
              message: '报文格式错误',
              type: 'error',
              showClose: true
            });
          } else if (res.errcode === 2) {
            serverStore.setCallbackValue(res);
          } else if (res.errcode === 404) {
            ElMessage({
              message: res.err_msg,
              type: 'error',
              showClose: true
            });
          } else {
            // 获取验证码
            if (res.method === 'get_verify_code_rsp' || res.method === 'report_verify_code') {
              rcuStore.setVerifyCode(res.info.code);
            }

            // 日志上报
            if (res.method === 'report_log') {
              rcuStore.addLog(res.info);
            }

            // 网关搜索上报
            if (res.method === 'search_gateway_info') {
              rcuStore.addGatewaySearch(res.info);
            }

            // 设备状态变化
            if (res.method === 'status_notify') {
              rcuStore.setDeviceStatus(res.info);
            }

            // 安防状态变化
            if (res.method === 'guard_mode_notify') {
              rcuStore.setGuardMode(res.info.mode);
            }

            if (res.method === 'complete') {
              // rcuStore.setAisceneComplete(res);
            }
          }
        });
        
        reader.readAsArrayBuffer(event.data as Blob);
      };
      
      ws.onerror = (error: Event) => {
        console.error(`WebSocket ${id} 错误:`, error);
      };

      // 保存实例到 store
      this.addConnection({ id, ws });
      return true;
    },

    // 添加连接
    addConnection({ id, ws }: { id: string, ws: WebSocket }) {
      this.connections[id] = ws;
      this.status[id] = 'connecting';
      this.messageQueue[id] = [];
      this.unacknowledgedMessages[id] = [];
    },
    
    // 更新连接状态
    updateStatus({ id, status }: { id: string, status: ConnectionStatus }) {
      this.status[id] = status;
    },
    
    // 移除连接
    removeConnection(id: string) {
      delete this.connections[id];
      delete this.status[id];
      delete this.messageQueue[id];
      delete this.unacknowledgedMessages[id];
    },
    
    // 移除所有连接
    removeAllConnections() {
      this.connections = {};
      this.status = {};
      this.messageQueue = {};
      this.unacknowledgedMessages = {};
    },
    
    // 添加消息到队列
    addMessageToQueue({ id, message }: { id: string, message: any }) {
      if (this.messageQueue[id]) {
        this.messageQueue[id].push(message);
      }
    },
    
    // 清空消息队列
    clearMessageQueue(id: string) {
      if (this.messageQueue[id]) {
        this.messageQueue[id] = [];
      }
    },
    
    // 添加未确认消息
    addUnacknowledgedMessage({ id, message }: { id: string, message: any }) {
      if (this.unacknowledgedMessages[id]) {
        this.unacknowledgedMessages[id].push(message);
      }
    },
    
    // 移除未确认消息
    removeUnacknowledgedMessage({ id }: { id: string }) {
      if (this.unacknowledgedMessages[id]) {
        this.unacknowledgedMessages[id] = [];
      }
    },

    // 发送消息
    sendMessage({ id, data }: { id: string, data: any }) {
      console.log(`ℹ️给${id}发消息了:`, data);
      const ws = this.connections[id] as WebSocket;
      
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(data);
        this.addUnacknowledgedMessage({ id, message: data }); // 将消息添加到未确认消息队列
      } else {
        this.addMessageToQueue({ id, message: data }); // 将消息添加到队列
      }
    },

    // 发送队列中的消息
    sendQueuedMessages(id: string) {
      const ws = this.connections[id] as WebSocket;
      const queue = this.messageQueue[id] || [];
      const unacknowledgedQueue = this.unacknowledgedMessages[id] || [];

      if (ws && ws.readyState === WebSocket.OPEN) {
        // 发送消息队列中的消息
        queue.forEach(message => {
          ws.send(JSON.stringify(message));
          this.addUnacknowledgedMessage({ id, message }); // 将消息添加到未确认消息队列
        });
        this.clearMessageQueue(id); // 清空消息队列

        // 发送未确认消息队列中的消息
        unacknowledgedQueue.forEach(message => {
          ws.send(JSON.stringify(message));
        });
      }
    },

    // 心跳检测
    startHeartbeat(id: string) {
      const ws = this.connections[id] as CustomWebSocket;
      if (!ws) return;
      
      let counter = 1;
      const heartbeatInterval = window.setInterval(() => {
        console.log(ws.readyState, counter++);
        if (ws.readyState === WebSocket.OPEN) {
          // ws.send('HEARTBEAT');
        } else {
          clearInterval(heartbeatInterval);
        }
      }, 1000); // 1秒一次心跳

      // 存储定时器ID以便清理
      ws.heartbeatTimer = heartbeatInterval;
    },

    // 重连机制
    reconnect(id: string) {
      const maxAttempts = 5;
      const ws = this.connections[id] as CustomWebSocket;
      
      if (!ws || (ws.reconnectAttempts || 0) >= maxAttempts) return;

      setTimeout(() => {
        console.log(`尝试重连 ${id}...`);
        ws.reconnectAttempts = (ws.reconnectAttempts || 0) + 1;
        this.initConnection({ id, url: ws.url });
      }, 5000);
    },

    // 关闭连接
    closeConnection(id: string) {
      const ws = this.connections[id] as CustomWebSocket;
      if (ws) {
        if (ws.heartbeatTimer) {
          clearInterval(ws.heartbeatTimer); // 清除心跳
        }
        ws.close();
        this.removeConnection(id);
      }
    },

    // 断开所有连接
    closeAllConnections() {
      Object.keys(this.connections).forEach(id => {
        const ws = this.connections[id] as CustomWebSocket;
        if (ws) {
          if (ws.heartbeatTimer) {
            clearInterval(ws.heartbeatTimer); // 清除心跳
          }
          ws.close();
        }
      });
      this.removeAllConnections();
    },
  }
});