<template>
  <div
    class="common-box index"
    v-loading.fullscreen.lock="isLoading"
    :element-loading-text="loadingText"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 1)"
  >
    <div class="top-bar">
      <div class="left">
        <span>系统时间：{{ systemInfo.currentTime }}</span>
        <el-button @click="getWebTime" type="primary" size="mini">同步</el-button>
        <el-button type="primary" size="mini" @click="doHost(0)">恢复出厂</el-button>
        <el-button type="primary" size="mini" @click="doHost(1)">重启主机</el-button>
      </div>
    </div>
  </div>
</template>



<script setup lang="ts">
import { useRcuStore } from '@/store/modules/rcu'
import { useWebsocketStore } from '@/store/modules/websocket'
import {
  getSysInfo,
  getNetworkConfig,
  setNetworkConfig,
  hostExportProj,
  upgradeHost,
  reboot,
  restore
} from '@/api/ServiceManage.js'
const rcuStore = useRcuStore()

const websocketStore = useWebsocketStore()
websocketStore.sendMessage({ id: 'socket', data: getSysInfo() })

// 获取状态
const systemInfo = computed(() => rcuStore.systemInfo)
import { computed, onMounted, reactive, toRefs } from 'vue'

const state = reactive({
  weatherIcon: '',
  current_city: {},
  airQualityText: ''
})

defineOptions({ name: 'Console' })
</script>

<style lang="scss" scoped>
@use './style';
</style>
