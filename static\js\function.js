import Vue from 'vue';

// 主机时间
export function hostTime(value) {
        if (!value) return "";
        var date = new Date(value * 1000);
        var seperator1 = "/";
        var seperator2 = ":";
        var month =
                date.getMonth() + 1 < 10
                        ? "0" + (date.getMonth() + 1)
                        : date.getMonth() + 1;
        var strDate = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
        var sHours =
                date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
        var sMinutes =
                date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();

        var sSeconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        var currentdate =
                date.getFullYear() +
                seperator1 +
                month +
                seperator1 +
                strDate +
                " " +
                sHours +
                seperator2 +
                sMinutes;
        return currentdate;
}
//日志时间,报警信息时间
export function logTime(value) {
        // console.log(value);
        var date = new Date(value);
        var YY = date.getFullYear() + "-";
        var MM =
                (date.getMonth() + 1 < 10
                        ? "0" + (date.getMonth() + 1)
                        : date.getMonth() + 1) + "-";
        var DD = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
        var hh =
                (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
        var mm =
                (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
                ":";
        var ss =
                (date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds()) +
                ":";

        if (date.getMilliseconds() < 10) {
                var ms = "00" + date.getMilliseconds();
        } else if (date.getMilliseconds() < 100 && date.getMilliseconds() >= 10) {
                var ms = "0" + date.getMilliseconds();
        } else {
                var ms = date.getMilliseconds();
        }

        return YY + MM + DD + " " + hh + mm + ss + ms;
}
// 主机运行时间
export function runTime(value) {
        if (!value) return "";
        var millisecond = value * 1000; //毫秒数
        var days = parseInt(millisecond / (1000 * 60 * 60 * 24));
        var hours = parseInt((millisecond % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = parseInt((millisecond % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = (millisecond % (1000 * 60)) / 1000;

        return days + "天" + hours + "小时" + minutes + "分钟";
}

// 生成UUID
export function generateUUID() {
        var d = new Date().getTime();
        if (window.performance && typeof window.performance.now === "function") {
                d += performance.now(); //use high-precision timer if available
        }
        var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
        return uuid;
}

// 数组对比
export function compare(property) {
        return function (a, b) {
                var value1 = a[property];
                var value2 = b[property];
                return value1 - value2;
        };
}
export function deviceSort(field1, field2) {
        return function (a, b) {
                if (a.field1 == b.field1) return a.field2 - b.field2;
                return a.field1 - b.field1;
        }
}

// 不同的设备属性   根据设备不同添加不同的属性
export function setAttr(devtype, type) {
        var attr = {};
        // 任务管理添加条件时的属性
        if (type == 'condition') {
                switch (devtype) {
                        case 'relay':
                        case 'do':
                        case 'key_led':
                        case 'backlight':
                        case 'voice_onoff':
                        case 'reply_onoff':
                                attr.onoff = 'close';
                                break;
                        case 'dim':
                        case 'dim_advance':
                                attr.level = '0';
                                break;
                        case 'colortemp':
                                attr.level = '';
                                attr.colortemp = '';
                                break;
                        case 'hvac_ac_ctrl':
                        case 'hvac_ac_ctrl2':
                        case 'hvac_ac_ctrl_gdy':
                        case 'hvac_ac_ctrl_iracc':
                        case 'acactuator':
                        case 'hvac_ac_dn_ctrl':
                        case 'hvac_dn_ctrl':
                        case 'hvac_ac_ctrl_zh':
                        case 'hvac_ac_dn_ctrl_zh':
                                attr.onoff = ''
                                attr.temp = '';
                                attr.roomtemp = '';
                                break;
                        case 'hvac_dn_ctrl_gdy':
                                attr.onoff = ''
                                attr.temp = '';
                                attr.roomtemp = '';
                                attr.heat = '';
                                break;
                        case 'hvac_xf_ctrl':
                        case 'hvac_xf_ctrl_zh':
                                attr.onoff = ''
                                attr.roomtemp = '';
                                break;

                        case 'di':
                        case 'key':
                        case 'voice':
                                attr.status = '';
                                break;
                        case 'sensor_env':
                                attr.temp = '';
                                attr.humidify = '';
                                attr.co2 = '';
                                attr.pm25 = '';
                                attr.tvoc = '';
                                break;
                        case 'key_advance':
                                attr.status = 'press';
                                break;
                        case 'virtual_object_uint32':
                                attr.value = '';
                                break;
                        case 'lock':
                                attr.alert = '';
                                attr.usr_id = '';
                                attr.low_pwr = '';
                                break;
                        case 'merrytek_exist':
                                attr.compare = '';
                                attr.light_sensor = '';
                                attr.state = '';
                                break;
                }
        }
        // 任务动作
        if (type == 'taskAction') {
                // 任务添加动作(可写w)
                // 灯光,调光去掉
                switch (devtype) {
                        case 'relay':
                        case 'do':
                                attr.cmd = 'close';
                                break;
                        case 'key_led':
                        case 'backlight':
                        case 'smart_circuit_breaker_ty630_1':
                        case 'smart_circuit_breaker_ty630_3':
                        case 'voice_onoff':
                        case 'reply_onoff':
                                attr.onoff = 'close';
                                break;
                        case 'dim':
                                attr.cmd = '';
                                attr.level = '';
                                break;
                        case 'dim_advance':
                                attr.cmd = '';
                                attr.level = '';
                                attr.transitiontime = '';
                                break;
                        case 'rgb':
                                attr.mode = '';
                                attr.level = '';
                                attr.red = '';
                                attr.green = '';
                                attr.blue = '';
                                attr.transitiontime = '';
                                attr.cmd = '';
                                break;
                        case 'colortemp':
                                attr.level = '';
                                attr.colortemp = '';
                                attr.transitiontime = '';
                                break;
                        case 'wireless_curtain_ctrl':
                        case 'curtain_kaihe':
                        case 'curtain_chuizhi':
                        case 'curtain_juanlian':
                        case 'curtain_baiye':
                        case 'curtain_pusher':
                        case 'curtain_menghuan':
                                attr.cmd = '';
                                break;
                        case 'hvac_ac_ctrl':
                        case 'hvac_ac_ctrl2':
                        case 'hvac_ac_ctrl_gdy':
                        case 'hvac_ac_ctrl_iracc':
                        case 'hvac_ac_dn_ctrl':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;
                        case 'acactuator':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                attr.intemp = '';
                                break;
                        case 'hvac_dn_ctrl':
                                attr.onoff = 'close';
                                attr.temp = '';
                                break;
                        case 'hvac_dn_ctrl_gdy':
                                attr.onoff = 'close';
                                attr.temp = '';
                                attr.heat = '';
                                break;
                        case 'hvac_xf_ctrl':
                                attr.onoff = 'close';
                                attr.loop = '';
                                attr.wind = '';
                                attr.humidify = ''; //加湿 
                                break;
                        case 'infrared_ac':
                                attr.onoff = '0';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;
                        // 影音主机红外空调
                        case 'ir_hxd_ac':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.direction = '';
                                attr.temp = '';
                                break;
                        case 'infrared_tv':
                        case 'infrared_stb':
                        case 'infrared_netbox':
                        case 'infrared_iptv':
                        case 'infrared_fan':
                        case 'infrared_projector':
                        case 'infrared_stereo':
                        case 'infrared_clean_robot':
                        case 'infrared_air_cleaner':
                        // 影音主机
                        case 'ir_hxd_fan':
                        case 'ir_hxd_tv':
                        case 'ir_hxd_stb':
                        case 'ir_hxd_iptv':
                        case 'ir_hxd_stereo':
                        case 'ir_hxd_projector':
                        case 'ir_hxd_air_purifier':
                        case 'ir_hxd_robotcleaner':
                                attr.cmd = '';
                                break;
                        case 'bgm_customize':
                        case 'bgm_runyo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.mode = '';
                                attr.cmd = '';
                                attr.source = '';
                                break;
                        case 'projector':
                                attr.onoff = 'close';
                                attr.input = '';
                                break;
                        case 'player':
                        case 'stereo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.cmd = '';
                                break;
                        case 'virtual_object_uint32':
                                attr.value = '';
                                break;
                        case 'lock':
                                attr.onoff = 'close';
                                break;
                        case 'bgm_audioback':
                                attr.cmd = '';
                                break;
                        // 新增
                        case 'hvac_ac_ctrl_zh':
                        case 'hvac_ac_dn_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.swing = '';
                                attr.temp = '';
                        case 'hvac_xf_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';

                }
        }
        // 功能关联动作
        if (type == 'funRelevance') {
                // 功能关联
                switch (devtype) {
                        case 'relay':
                        case 'do':
                        case 'smart_circuit_breaker_ty630_1':
                        case 'smart_circuit_breaker_ty630_3':
                                attr.cmd = 'close';
                                break;
                        case 'key_led':
                        case 'backlight':
                        case 'voice_onoff':
                        case 'reply_onoff':
                                attr.onoff = 'close';
                                break;
                        case 'dim':
                                attr.cmd = '';
                                attr.level = '';
                                break;
                        case 'dim_advance':
                                attr.cmd = '';
                                attr.level = '';
                                attr.transitiontime = '';
                                break;
                        case 'rgb':
                                attr.mode = 'normal'; // 0普通模式 1呼吸模式 2炫彩模式 
                                attr.level = '';
                                attr.red = ''; //0-255
                                attr.green = '';
                                attr.blue = '';
                                attr.transitiontime = '';
                                attr.cmd = '';
                                break;
                        case 'colortemp':
                                attr.level = '0'; //0-100
                                attr.colortemp = '';  //0-100色温    0-50 暖光  0最暖  50-100 冷光 100最冷
                                attr.transitiontime = ''
                                break;
                        case 'wireless_curtain_ctrl':
                        case 'curtain_kaihe':
                        case 'curtain_chuizhi':
                        case 'curtain_juanlian':
                        case 'curtain_pusher':
                        case 'curtain_baiye':
                        case 'curtain_menghuan':
                                attr.cmd = 'close';
                                break;
                        case 'hvac_ac_ctrl':
                        case 'hvac_ac_ctrl2':
                        case 'hvac_ac_ctrl_gdy':
                        case 'hvac_ac_ctrl_iracc':
                                attr.onoff = '';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;
                        case 'hvac_ac_dn_ctrl':
                                attr.onoff = '';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                attr.roomtemp = '';
                                break;
                        case 'hvac_dn_ctrl':
                                attr.onoff = '';
                                attr.temp = '';
                                break;
                        case 'hvac_dn_ctrl_gdy':
                                attr.onoff = '';
                                attr.temp = '';
                                attr.heat = '';
                                break;
                        case 'hvac_xf_ctrl':
                                attr.onoff = '';
                                attr.loop = '';
                                attr.wind = '';
                                attr.humidify = '';
                                break;
                        case 'acactuator':
                                attr.onoff = '';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                attr.intemp = '';
                                break;
                        case 'infrared_ac':
                                attr.onoff = '0';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;
                        // 影音主机红外空调
                        case 'ir_hxd_ac':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.direction = '';
                                attr.temp = '';
                                break;
                        case 'infrared_tv':
                        case 'infrared_stb':
                        case 'infrared_netbox':
                        case 'infrared_iptv':
                        case 'infrared_fan':
                        case 'infrared_projector':
                        case 'infrared_stereo':
                        case 'infrared_clean_robot':
                        case 'infrared_air_cleaner':
                        // 影音主机
                        case 'ir_hxd_fan':
                        case 'ir_hxd_tv':
                        case 'ir_hxd_stb':
                        case 'ir_hxd_iptv':
                        case 'ir_hxd_stereo':
                        case 'ir_hxd_projector':
                        case 'ir_hxd_air_purifier':
                        case 'ir_hxd_robotcleaner':
                                attr.cmd = '';
                                break;
                        case 'bgm_customize':
                        case 'bgm_runyo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.cmd = '';
                                attr.source = '';
                                break;
                        case 'projector':
                                attr.onoff = 'close';
                                attr.input = '';
                                break;
                        case 'player':
                        case 'stereo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.cmd = '';
                                break;
                        case 'virtual_object_uint32':
                                attr.value = '';
                                break;
                        case 'lock':
                                attr.onoff = 'close';
                                break;
                        case 'bgm_audioback':
                                attr.cmd = '';
                                break;
                        // 新增
                        case 'hvac_ac_ctrl_zh':
                        case 'hvac_ac_dn_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.swing = '';
                                attr.temp = '';
                        case 'hvac_xf_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';

                }
        }
        // 场景
        if (type == 'sceneMode') {
                switch (devtype) {
                        case 'relay':
                        case 'do':
                        case 'key_led':
                        case 'backlight':
                        case 'smart_circuit_breaker_ty630_1':
                        case 'smart_circuit_breaker_ty630_3':
                        case 'voice_onoff':
                        case 'reply_onoff':
                                attr.onoff = 'close';
                                break;
                        case 'dim':
                                attr.level = '';
                                break;
                        case 'dim_advance':
                                attr.level = '';
                                attr.transitiontime = '';
                                break;
                        case 'rgb':
                                attr.mode = 'normal'; // normal普通模式  breath呼吸模式   colorful炫彩模式 
                                attr.level = '';
                                attr.red = ''; //0-255
                                attr.green = '';
                                attr.blue = '';
                                attr.transitiontime = '';
                                attr.cmd = '';
                                break;
                        case 'colortemp':
                                attr.level = ''; //0-100
                                attr.colortemp = '';  //0-100色温    0-50 暖光  0最暖  50-100 冷光 100最冷
                                attr.transitiontime = '';
                                break;
                        case 'wireless_curtain_ctrl':
                        case 'curtain_customize':
                                attr.cmd = 'close';
                                break;
                        case 'curt_weishida':
                                attr.cmd = 'close';
                                attr.level = '';
                                break;
                        case 'curtain_kaihe':
                        case 'curtain_chuizhi':
                        case 'curtain_juanlian':
                        case 'curtain_baiye':
                                attr.level = '';
                                break;
                        case 'curtain_menghuan':
                                attr.level = '';
                                attr.angle = ''
                                break;
                        case 'hvac_ac_ctrl':
                        case 'hvac_ac_ctrl2':
                        case 'hvac_ac_ctrl_gdy':
                        case 'hvac_ac_ctrl_iracc':
                        case 'hvac_ac_dn_ctrl':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;

                        case 'hvac_dn_ctrl':
                                attr.onoff = 'close';
                                attr.temp = '';
                                break;
                        case 'hvac_dn_ctrl_gdy':
                                attr.onoff = 'close';
                                attr.temp = '';
                                attr.heat = '';
                                break;
                        case 'hvac_xf_ctrl':
                                attr.onoff = 'close';
                                attr.loop = '';
                                attr.wind = '';
                                attr.humidify = ''; //加湿
                                break;
                        case 'acactuator':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                attr.intemp = '';
                                break;
                        case 'infrared_ac':
                                attr.onoff = '0';
                                attr.mode = '';
                                attr.wind = '';
                                attr.temp = '';
                                break;
                        // 影音主机红外空调
                        case 'ir_hxd_ac':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.direction = '';
                                attr.temp = '';
                                break;
                        case 'infrared_tv':
                        case 'infrared_stb':
                        case 'infrared_netbox':
                        case 'infrared_iptv':
                        case 'infrared_fan':
                        case 'infrared_projector':
                        case 'infrared_stereo':
                        case 'infrared_clean_robot':
                        case 'infrared_air_cleaner':
                        // 影音主机
                        case 'ir_hxd_fan':
                        case 'ir_hxd_tv':
                        case 'ir_hxd_stb':
                        case 'ir_hxd_iptv':
                        case 'ir_hxd_stereo':
                        case 'ir_hxd_projector':
                        case 'ir_hxd_air_purifier':
                        case 'ir_hxd_robotcleaner':
                                attr.cmd = '';
                                break;
                        case 'bgm_customize':
                        case 'bgm_runyo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.cmd = '';
                                attr.source = '';
                                break;
                        case 'projector':
                                attr.onoff = 'close';
                                attr.input = '';
                                break;
                        case 'player':
                        case 'stereo':
                                attr.onoff = 'close';
                                attr.volume = '';
                                attr.cmd = '';
                                break;
                        case 'virtual_object_uint32':
                                attr.value = '';
                                break;
                        case 'lock':
                                attr.onoff = 'close';
                                break;
                        case 'bgm_audioback':
                                attr.cmd = '';
                                break;
                        // 新增
                        case 'hvac_ac_ctrl_zh':
                        case 'hvac_ac_dn_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';
                                attr.swing = '';
                                attr.temp = '';
                        case 'hvac_xf_ctrl_zh':
                                attr.onoff = 'close';
                                attr.mode = '';
                                attr.wind = '';

                }
        }
        // console.log(attr);
        return attr;
}

// 十六进制转换rgb
export function toRgb(hex) {
        if (!hex) return;
        var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
        var color = hex.toLowerCase();
        if (reg.test(color)) {
                // 如果只有三位的值，需变成六位，如：#fff => #ffffff
                if (color.length === 4) {
                        var colorNew = "#";
                        for (var i = 1; i < 4; i += 1) {
                                colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
                        }
                        color = colorNew;
                }
                // 处理六位的颜色值，转为RGB
                var colorChange = [];
                for (var i = 1; i < 7; i += 2) {
                        colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
                }
                return colorChange;

        } else {
                return color;
        }
}

// 过滤器
export function filterRgb(hex) {
        if (!hex) return;
        var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
        var color = hex.toLowerCase();
        if (reg.test(color)) {
                // 如果只有三位的值，需变成六位，如：#fff => #ffffff
                if (color.length === 4) {
                        var colorNew = "#";
                        for (var i = 1; i < 4; i += 1) {
                                colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
                        }
                        color = colorNew;
                }
                // 处理六位的颜色值，转为RGB
                var colorChange = [];
                for (var i = 1; i < 7; i += 2) {
                        colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
                }
                // console.log(colorChange);
                return "R " + colorChange[0] + ', G ' + colorChange[1] + ', B ' + colorChange[2];
                // return "RGB(" + colorChange.join(",") + ")";
        } else {
                return color;
        }
}

export function ruleServer(ev) {
        var value = ev.target.value;
        var pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]*[a-zA-Z0-9])\.)+([A-Za-z]|[A-Za-z][A-Za-z0-9\-]*[A-Za-z0-9])$/;

        if (!pattern.test(value)) {

                Vue.prototype.$message({
                        message: '请输入合理的IP地址或域名',
                        showClose: true
                })
                ev.target.value = ''
        }
}

// 区域房间显示
export function funRegion(vid, list) {
        // console.log(list)
        var str = ' '
        list.map((item, ins) => {
                if (vid == item.id) {
                        str = item.name;
                }
        })

        return str;
}
export function funRoom(vid, list) {
        // console.log(list);
        var str = ' '
        list.map((item, ins) => {
                if (vid == item.id) {
                        str = item.name;
                }
        })

        return str;
}

// 模块生成设备
export function setDevice(devtype) {
        var obj = {
                devtype:devtype,
                module_name: devtype,
                module_id: ''
        }

        if(devtype == 'SE-SW-TY'){
                obj.relay_list = []
        }




        return obj;
}