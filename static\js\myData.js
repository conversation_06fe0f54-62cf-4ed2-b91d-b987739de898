 
export let my_sebus_list = [
        {
                busid: '',
                devtype: "se-sw4-d",
                name: "SE-SW4-D", //继电器模块
                vid: "",
                relay_list: function () {
                        var arr = [];
                        for (var i = 0; i < 4; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "sw"+(i+1),
                                        region_id: "",
                                        room_id: "",
                                        enable: "true", //是否禁用
                                        visible: "true",  //app是否显示
                                        subtype: 'light', //设备能力
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },
        // sw8
        {
                busid: '',
                devtype: "se-sw8-d",
                name: "SE-SW8-D", //继电器模块
                vid: "",
                relay_list: function () {
                        var arr = []
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "sw"+(i+1),
                                        region_id: "",
                                        room_id: "",
                                        enable: "true", //是否禁用
                                        visible: "true",  //app是否显示
                                        subtype: 'light',//设备能力
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },

        // 新增do
        {
                busid: '',
                devtype: "se-do10-d",
                name: "SE-DO10-D", 
                vid: "",
                list: function () {
                        var arr = []
                        for (var i = 0; i < 10; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "do"+(i+1),
                                        region_id: "",
                                        room_id: "",
                                        enable: "true", //是否禁用
                                        visible: "true",  //app是否显示
                                        subtype: 'onoff',//设备能力
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },
        // dim4
        {
                busid: '',
                devtype: "se-dim4-d",
                name: "SE-DIM4-D", //调光模块
                vid: "",
                dim_list: function () {
                        var arr = []
                        for (var i = 0; i < 4; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "调光" + (i + 1),
                                        region_id: "",
                                        room_id: "",
                                        enable: "true", //是否禁用
                                        visible: "true",  //app是否显示
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },

        // dali
        {
                busid: '',
                devtype: "se-dali-d",
                name: "SE-DALI-D", //调光模块
                vid: "",
                // dalicnt:null,
                dali_list: [],
                dali_scene_list: [],
                dali_group_list: []
        },

        // hub
        {
                busid: '',
                devtype: "se-hub-da",
                name: "SE-HUB-DA", //HUB模块
                vid: "",
                dev_list: []
        },
        // 新增智能空开
        {
                busid: '',
                devtype: "se-hub-as-ty",
                name: "SE-HUB-AS-TY", //HUB模块
                vid: "",
                dev_list: []
        },
        // 无线窗帘  
        {
                busid: '',
                devtype: "se-curf4",
                name: "SE-CURF4",
                vid: "",
                ctrl_list: function () {
                        var arr = []
                        for (var i = 0; i < 4; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "无线窗帘控制器" + (i + 1),
                                        region_id: "",
                                        room_id: "",
                                        enable: "false",  //是否禁用
                                        visible: "true"  //app显示
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },
        // 16路
        {
                busid: '',
                devtype: "se-16i-d",
                name: "SE-16I-D",
                vid: "",
                di_list: function () {
                        var arr = []
                        for (var i = 0; i < 16; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "di" + (i + 1),
                                        region_id: "",
                                        room_id: "",
                                        subtype: 'di',
                                        trigger_type:'open',
                                        enable: "false", //是否禁用
                                        visible: "true",//app显示,

                                }
                                arr.push(obj);
                        }
                        return arr;
                }()
        },
        // rgb
        {
                busid: '',
                devtype: "g-rgb-es",
                name: "G-RGB-ES", //RGB模块
                vid: "",
                ability: [
                        {
                                chn: 1,
                                type: "colortemp",
                                vid: "",
                                title: "色温",
                                name: "色温",
                                region_id: "",
                                room_id: "",
                                enable: "false",
                                visible: "false",
                                coord_type: 'empty'
                        },
                        {
                                chn: 2,
                                type: "rgb",
                                vid: "",
                                title: "RGB",
                                name: "RGB",
                                region_id: "",
                                room_id: "",
                                enable: "false",
                                visible: "false",
                                coord_type: 'empty'
                        }
                ]
        },

        // skpxn
        {
                busid: '',
                devtype: "gdy-skpxn-es",
                name: "GDY-SKPXN-ES", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 6; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }
                        return arr;
                }(),
                key_led_list: function () {
                        var arr = [];
                        for (var i = 0; i < 6; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '指示灯' + (i + 1)
                                }
                                arr.push(obj)
                        }
                        return arr;
                }(),

                backlight: {
                        name: "背景灯",
                        vid: ""
                }
        },
        //     新增
        {
                busid: '',
                devtype: "gdy-skpx-nes",
                name: "GDY-SKPX-NES", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                backlight: {
                        name: "背景灯",
                        vid: ""
                },
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }
                        arr.push({
                                chn: 9,
                                name: '感应',
                                vid: ''
                        })
                        return arr;
                }(),

        },


        {
                busid: '',
                devtype: "gdy-skpx-nes-sw",
                name: "GDY-SKPX-NES-SW", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                backlight: {
                        name: "背景灯",
                        vid: ""
                },
                human_detection: {
                        name: "红外感应",
                        vid: ""
                },
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }

                        return arr;
                }(),
                relay_list: function () {
                        var arr = [];
                        for (var i = 0; i < 4; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "",
                                        region_id: "",
                                        room_id: "",
                                        enable: "false", //是否禁用
                                        visible: "true",  //app是否显示
                                        subtype: 'light',//设备能力
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()

        },
        {
                busid: '',
                devtype: "gdy-skpx-nes-swn",
                name: "GDY-SKPX-NES-SWN", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                backlight: {
                        name: "背景灯",
                        vid: ""
                },
                human_detection: {
                        name: "红外感应",
                        vid: ""
                },
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }

                        return arr;
                }(),
                key_led_list: function () {
                        var arr = [];
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '指示灯' + (i + 1)
                                }
                                arr.push(obj)
                        }
                        return arr;
                }(),
                relay_list: function () {
                        var arr = [];
                        for (var i = 0; i < 4; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: "",
                                        name: "",
                                        region_id: "",
                                        room_id: "",
                                        enable: "false", //是否禁用
                                        visible: "true",  //app是否显示
                                        subtype: 'light',//设备能力
                                        coord_type: 'empty'
                                }
                                arr.push(obj);
                        }
                        return arr;
                }()

        },

        // 新增典恒语音面板
        {
                busid: '',
                devtype: "gdy-skpx-nes-is",
                name: "GDY-SKPX-NES-IS", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                bind_ac_list: [
                        {
                                vid: ''
                        }
                ],
                backlight: {
                        name: "背景灯",
                        vid: ""
                },
                voiceOnoff:{
                     name:'语音开关',
                     vid:''
                },
                replyOnoff:{
                    name:'回复开关',
                    vid:''
                },
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 8; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }
                        arr.push({
                                chn: 9,
                                name: '感应',
                                vid: ''
                        })
                        return arr;
                }()
        },

        // 新增空调面板
        {
                busid: '',
                devtype: "gdy-kpac-es",
                name: "GDY- KPAC-ES", //HUB模块
                vid: "",
                ac_list: [
                        {
                                vid: '',
                                name: "空调",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_ac_list: [{ vid: '' }]
                        }
                ]
        },

        {
                busid: '',
                devtype: "gdy-kpfh-es",
                name: "GDY- KPFH-ES",
                vid: "",

                dn_list: [
                        {
                                vid: '',
                                name: "地暖",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_dn_list: [{ vid: '' }]
                        }
                ]
        },
        // 新增三合一
        {
                busid: '',
                devtype: "gdy-tphc-es",
                name: "GDY- TPHC-ES", //HUB模块
                vid: "",
                ac_list: [
                        {
                                vid: '',
                                name: "空调",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_ac_list: [{ vid: '' }]
                        }
                ],
                dn_list: [
                        {
                                vid: '',
                                name: "地暖",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_dn_list: [{ vid: '' }]
                        }
                ],
                xf_list: [
                        {
                                vid: '',
                                name: "新风",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'false',
                                coord_type: 'empty',
                                bind_xf_list: [{ vid: '' }]
                        }
                ]
        },
        // 复制三合一
        {
                busid: '',
                devtype: "gdy-tphc-h-es",
                name: "GDY- TPHC-H-ES",  
                vid: "",
                ac_list: [
                        {
                                vid: '',
                                name: "空调",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_ac_list: [{ vid: '' }]
                        }
                ],
                dn_list: [
                        {
                                vid: '',
                                name: "地暖",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'true',
                                coord_type: 'empty',
                                bind_dn_list: [{ vid: '' }]
                        }
                ],
                xf_list: [
                        {
                                vid: '',
                                name: "新风",
                                region_id: "",
                                room_id: "",
                                enable: "true",
                                visible: 'false',
                                coord_type: 'empty',
                                bind_xf_list: [{ vid: '' }]
                        }
                ]
        },
        // 新增屏
        {
                busid: '',
                devtype: "gdy-skpx-tp-es",
                name: "GDY-SKPX-TP-ES", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                key_list: [],//按键
                relay_list: [], //继电器
                curtain_list: [],//窗帘
                ac_list: [],//空调
                dn_list: [],//地暖
                xf_list: []  //新风
        },
        // 新增屏-1
        {
                busid: '',
                devtype: "gdy-skpx-tpd-es",
                name: "GDY-SKPX-TPD-ES", //智能面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                key_list: [],//按键
                light_list: [], //灯光
                curtain_list: [],//窗帘
                ac_list: [],//空调
                dn_list: [],//地暖
                xf_list: []  //新风
        },
        // 新增弱电板

        {
                busid: '',
                devtype: "gdy-skpx-lacl-es", //gdy-skpx-lacw-es
                name: "GDY-SKPX-LACL-ES", //GDY-SKPX-LACW-ES
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                light_list: [
                        {
                                vid: "",
                                chn: 1,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 2,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 3,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 4,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        }

                ], //灯光
                ac_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                chn: 1,
                                enable:'false'
                        }
                ],//空调
                dn_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                chn: 1,
                                enable:'false'
                        }
                ],//地暖
                xf_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                chn: 1,
                                enable:'false',
                        }
                ]  //新风
        },

        // 新增强电板
        {
                busid: '',
                devtype: "gdy-skpx-lach-es",
                name: "GDY-SKPX-LACH-ES",
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                light_list: [
                        {
                                vid: "",
                                chn: 1,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 2,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 3,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        },
                        {
                                vid: "",
                                chn: 4,
                                type: '',
                                name: "",
                                region_id: "",
                                room_id: "",
                        }

                ], //灯光
                ac_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                visible: 'true',
                                enable:'false',
                                chn: 1,
                                coord_type: 'empty'
                        }
                ],//空调
                dn_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                visible: "true",
                                enable:'false',
                                chn: 1,
                                coord_type: 'empty'
                        }
                ],//地暖
                xf_list: [
                        {
                                vid: "",
                                name: "",
                                region_id: "",
                                room_id: "",
                                visible: "true",
                                enable:'false',
                                chn: 1,
                                coord_type: 'empty'
                        }
                ]  //新风
        },

        {
                busid: '',
                devtype: "apanel-mcis-es",
                name: "APANEL-MCIS-ES", //语音面板
                vid: "",
                tempctrl_vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                bind_ac_list: [
                        {
                                vid: ''
                        }
                ],
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 16; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }

                        return arr;
                }(),

                voice_list: [],
                backlight: {
                        name: "背景灯",
                        vid: ""
                },
                voiceOnoff:{
                        name:'语音开关',
                        vid:''
                },
                replyOnoff:{
                        name:'回复开关',
                       vid:''
                }
        },
        {
                busid: '',
                devtype: "apanel-mces-es",
                name: "APANEL-MCES-ES", //魔方面板
                vid: "",
                region_id: "",
                room_id: "",
                enable: "false",
                visible: "true",
                key_list: function () {
                        var arr = [];
                        for (var i = 0; i < 16; i++) {
                                var obj = {
                                        chn: i + 1,
                                        vid: '',
                                        name: '按键' + (i + 1)
                                }
                                arr.push(obj)
                        }

                        return arr;
                }(),

                backlight: {
                        name: "背景灯",
                        vid: ""
                }
        },
]

// 情景模式

// 红外类cmd
export let class_infrared_cmd = [
        {
                devtype: 'infrared_tv',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },
                        {
                                label: '静音',
                                value: '2'
                        },
                        {
                                label: '上',
                                value: '3'
                        },
                        {
                                label: '下',
                                value: '4'
                        },
                        {
                                label: '左',
                                value: '5'
                        },
                        {
                                label: '右',
                                value: '6'
                        },
                        {
                                label: '确定',
                                value: '7'
                        },
                        {
                                label: '音量+',
                                value: '8'
                        },
                        {
                                label: '音量-',
                                value: '9'
                        },
                        {
                                label: '返回',
                                value: '10'
                        },
                        {
                                label: '输入',
                                value: '11'
                        },
                        {
                                label: '菜单',
                                value: '12'
                        },
                        {
                                label: '主页',
                                value: '13'
                        },
                        {
                                label: '设置',
                                value: '14'
                        }
                ]
        },
        {
                devtype: 'infrared_stb',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },
                        {
                                label: '静音',
                                value: '2'
                        },
                        {
                                label: '上',
                                value: '3'
                        },
                        {
                                label: '下',
                                value: '4'
                        },
                        {
                                label: '左',
                                value: '5'
                        },
                        {
                                label: '右',
                                value: '6'
                        },
                        {
                                label: '确定',
                                value: '7'
                        },
                        {
                                label: '音量+',
                                value: '8'
                        },
                        {
                                label: '音量-',
                                value: '9'
                        },
                        {
                                label: '返回',
                                value: '10'
                        },
                        {
                                label: '输入',
                                value: '11'
                        },
                        {
                                label: '菜单',
                                value: '12'
                        },
                        {
                                label: '上一页',
                                value: '13'
                        },
                        {
                                label: '下一页',
                                value: '14'
                        }
                ]
        },
        {
                devtype: 'infrared_iptv',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },
                        {
                                label: '静音',
                                value: '2'
                        },
                        {
                                label: '上',
                                value: '3'
                        },
                        {
                                label: '下',
                                value: '4'
                        },
                        {
                                label: '左',
                                value: '5'
                        },
                        {
                                label: '右',
                                value: '6'
                        },
                        {
                                label: '确定',
                                value: '7'
                        },
                        {
                                label: '音量+',
                                value: '8'
                        },
                        {
                                label: '音量-',
                                value: '9'
                        },
                        {
                                label: '返回',
                                value: '10'
                        },
                        {
                                label: '输入',
                                value: '11'
                        },
                        {
                                label: '菜单',
                                value: '12'
                        },
                        {
                                label: '上一页',
                                value: '13'
                        },
                        {
                                label: '下一页',
                                value: '14'
                        }
                ]
        },
        {
                devtype: 'infrared_netbox',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },

                        {
                                label: '上',
                                value: '2'
                        },
                        {
                                label: '下',
                                value: '3'
                        },
                        {
                                label: '左',
                                value: '4'
                        },
                        {
                                label: '右',
                                value: '5'
                        },
                        {
                                label: '确定',
                                value: '6'
                        },
                        {
                                label: '音量+',
                                value: '7'
                        },
                        {
                                label: '音量-',
                                value: '8'
                        },
                        {
                                label: '返回',
                                value: '9'
                        },

                        {
                                label: '菜单',
                                value: '10'
                        },
                        {
                                label: '主页',
                                value: '11'
                        }
                ]
        },
        {
                devtype: 'infrared_fan',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },
                        {
                                label: '风速切换',
                                value: '2'
                        },
                        {
                                label: '摆风',
                                value: '3'
                        },
                        {
                                label: '风速加',
                                value: '4'
                        },
                        {
                                label: '风速减',
                                value: '5'
                        },
                        {
                                label: '风种类切换',
                                value: '6'
                        }
                ]
        },
        {
                devtype: 'infrared_projector',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },
                        {
                                label: '上',
                                value: '2'
                        },
                        {
                                label: '下',
                                value: '3'
                        },
                        {
                                label: '左',
                                value: '4'
                        },
                        {
                                label: '右',
                                value: '5'
                        },
                        {
                                label: '确定',
                                value: '6'
                        },
                        {
                                label: '音量+',
                                value: '7'
                        },
                        {
                                label: '音量-',
                                value: '8'
                        },
                        {
                                label: '缩小',
                                value: '9'
                        },
                        {
                                label: '菜单',
                                value: '10'
                        },
                        {
                                label: '放大',
                                value: '11'
                        },
                        {
                                label: '返回',
                                value: '12'
                        }
                ]
        },
        {
                devtype: 'infrared_stereo',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },

                        {
                                label: '上',
                                value: '2'
                        },
                        {
                                label: '下',
                                value: '3'
                        },
                        {
                                label: '左',
                                value: '4'
                        },
                        {
                                label: '右',
                                value: '5'
                        },
                        {
                                label: '确定',
                                value: '6'
                        },
                        {
                                label: '音量+',
                                value: '7'
                        },
                        {
                                label: '音量-',
                                value: '8'
                        },
                        {
                                label: '静音',
                                value: '9'
                        },

                        {
                                label: '菜单',
                                value: '10'
                        }
                ]
        },
        {
                devtype: 'infrared_clean_robot',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },

                        {
                                label: '向前',
                                value: '2'
                        },
                        {
                                label: '向后',
                                value: '3'
                        },
                        {
                                label: '左',
                                value: '4'
                        },
                        {
                                label: '右',
                                value: '5'
                        },
                        {
                                label: '开始',
                                value: '6'
                        },
                        {
                                label: '停止',
                                value: '7'
                        },
                        {
                                label: '自动',
                                value: '8'
                        },
                        {
                                label: 'SPOT',
                                value: '9'
                        },

                        {
                                label: '速度',
                                value: '10'
                        },
                        {
                                label: 'TIMMER',
                                value: '11'
                        },
                        {
                                label: '充电',
                                value: '12'
                        },
                        {
                                label: '保留',
                                value: '13'
                        }
                ]
        },
        {
                devtype: 'infrared_air_cleaner',
                arr: [
                        {
                                label: '开关',
                                value: '1'
                        },

                        {
                                label: '负离子',
                                value: '2'
                        },
                        {
                                label: '自动',
                                value: '3'
                        },
                        {
                                label: '风速',
                                value: '4'
                        },
                        {
                                label: '模式',
                                value: '5'
                        },
                        {
                                label: '定时',
                                value: '6'
                        },
                        {
                                label: '灯光',
                                value: '7'
                        },
                        {
                                label: '强力',
                                value: '8'
                        }
                ]
        },
        // 影音主机红外新增
        {
                devtype: 'ir_hxd_fan',
                arr: [
                        {
                                label: '开关',
                                value: 'onoff'
                        },
                        {
                                label: '风速',
                                value: 'wind'
                        },
                        {
                                label: '摇头',
                                value: 'swing'
                        },
                        {
                                label: '风类',
                                value: 'mode'
                        },
                        {
                                label: '定时',
                                value: 'timer'
                        },
                        {
                                label: '灯光',
                                value: 'light'
                        },
                        {
                                label: '负离子',
                                value: 'anion'
                        },
                        {
                                label: '睡眠',
                                value: 'sleep'
                        },
                        {
                                label: '制冷',
                                value: 'cold'
                        },
                        {
                                label: '风量',
                                value: 'air'
                        },
                        {
                                label: '低速',
                                value: 'low'
                        },
                        {
                                label: '中速',
                                value: 'mid'
                        },
                        {
                                label: '高速',
                                value: 'hi'
                        },
                        {
                                label: '1',
                                value: '1'
                        },
                        {
                                label: '2',
                                value: '2'
                        },
                        {
                                label: '3',
                                value: '3'
                        },
                        {
                                label: '4',
                                value: '4'
                        },
                        {
                                label: '5',
                                value: '5'
                        },
                        {
                                label: '6',
                                value: '6'
                        },
                        {
                                label: '7',
                                value: '7'
                        },
                        {
                                label: '8',
                                value: '8'
                        },
                        {
                                label: '9',
                                value: '9'
                        },
                ]
        },
        {
                devtype: 'ir_hxd_tv',
                arr: [
                        {
                                label: '电源',
                                value: 'power'
                        },
                        {
                                label: '音量+',
                                value: 'vol+'
                        },
                        {
                                label: '音量-',
                                value: 'vol-'
                        },
                        {
                                label: '频道+',
                                value: 'ch+'
                        },
                        {
                                label: '频道-',
                                value: 'ch-'
                        },
                        {
                                label: '静音',
                                value: 'mute'
                        },
                        {
                                label: '菜单',
                                value: 'menu'
                        },
                        {
                                label: '返回',
                                value: 'back'
                        },
                        {
                                label: '确定',
                                value: 'ok'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        },
                        {
                                label: '主页',
                                value: 'main'
                        },
                        {
                                label: 'AV/TV',
                                value: 'AV/TV'
                        },
                        {
                                label: '1',
                                value: '1'
                        },
                        {
                                label: '2',
                                value: '2'
                        },
                        {
                                label: '3',
                                value: '3'
                        },
                        {
                                label: '4',
                                value: '4'
                        },
                        {
                                label: '5',
                                value: '5'
                        },
                        {
                                label: '6',
                                value: '6'
                        },
                        {
                                label: '7',
                                value: '7'
                        },
                        {
                                label: '8',
                                value: '8'
                        },
                        {
                                label: '9',
                                value: '9'
                        },
                        {
                                label: '0',
                                value: '0'
                        },
                ]
        },
        {
                devtype: 'ir_hxd_stb',
                arr: [
                        {
                                label: '待机',
                                value: 'standby'
                        },
                        {
                                label: '音量+',
                                value: 'vol+'
                        },
                        {
                                label: '音量-',
                                value: 'vol-'
                        },
                        {
                                label: '频道+',
                                value: 'ch+'
                        },
                        {
                                label: '频道-',
                                value: 'ch-'
                        },
                        {
                                label: '菜单',
                                value: 'menu'
                        },
                        {
                                label: '返回',
                                value: 'back'
                        },
                        {
                                label: '确定',
                                value: 'ok'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        },
                        {
                                label: '导视',
                                value: 'guide'
                        },
                        {
                                label: '1',
                                value: '1'
                        },
                        {
                                label: '2',
                                value: '2'
                        },
                        {
                                label: '3',
                                value: '3'
                        },
                        {
                                label: '4',
                                value: '4'
                        },
                        {
                                label: '5',
                                value: '5'
                        },
                        {
                                label: '6',
                                value: '6'
                        },
                        {
                                label: '7',
                                value: '7'
                        },
                        {
                                label: '8',
                                value: '8'
                        },
                        {
                                label: '9',
                                value: '9'
                        },
                        {
                                label: '0',
                                value: '0'
                        },
                ]
        },
        {
                devtype: 'ir_hxd_iptv',
                arr: [
                        {
                                label: '电源',
                                value: 'power'
                        },
                        {
                                label: '静音',
                                value: 'mute'
                        },
                        {
                                label: '音量+',
                                value: 'vol+'
                        },
                        {
                                label: '音量-',
                                value: 'vol-'
                        },
                        {
                                label: '频道+',
                                value: 'ch+'
                        },
                        {
                                label: '频道-',
                                value: 'ch-'
                        },
                        {
                                label: '菜单',
                                value: 'menu'
                        },
                        {
                                label: '返回',
                                value: 'back'
                        },
                        {
                                label: '确定',
                                value: 'ok'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        },
                        {
                                label: '暂停',
                                value: 'pause'
                        },
                        {
                                label: '1',
                                value: '1'
                        },
                        {
                                label: '2',
                                value: '2'
                        },
                        {
                                label: '3',
                                value: '3'
                        },
                        {
                                label: '4',
                                value: '4'
                        },
                        {
                                label: '5',
                                value: '5'
                        },
                        {
                                label: '6',
                                value: '6'
                        },
                        {
                                label: '7',
                                value: '7'
                        },
                        {
                                label: '8',
                                value: '8'
                        },
                        {
                                label: '9',
                                value: '9'
                        },
                        {
                                label: '0',
                                value: '0'
                        },
                ]
        },
        {
                devtype: 'ir_hxd_stereo',
                arr: [
                        {
                                label: '电源',
                                value: 'power'
                        },
                        {
                                label: '静音',
                                value: 'mute'
                        },
                        {
                                label: '音量+',
                                value: 'vol+'
                        },
                        {
                                label: '音量-',
                                value: 'vol-'
                        },
                        {
                                label: '播放',
                                value: 'play'
                        },
                        {
                                label: '快进',
                                value: 'fast-forward'
                        },
                        {
                                label: '快退',
                                value: 'fast-rewind'
                        },
                        {
                                label: '上一首',
                                value: 'previous'
                        },
                        {
                                label: '下一首',
                                value: 'next'
                        },
                        {
                                label: '停止',
                                value: 'stop'
                        },
                        {
                                label: '暂停',
                                value: 'pause'
                        },
                        {
                                label: '菜单',
                                value: 'menu'
                        },
                        {
                                label: '返回',
                                value: 'back'
                        },
                        {
                                label: '确定',
                                value: 'ok'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        }
                ]
        },
        {
                devtype: 'ir_hxd_projector',
                arr: [
                        {
                                label: '开机',
                                value: 'open'
                        },
                        {
                                label: '关机',
                                value: 'close'
                        },
                        {
                                label: '电脑',
                                value: 'pc'
                        },
                        {
                                label: '视频',
                                value: 'video'
                        },
                        {
                                label: '信号源',
                                value: 'source'
                        },
                        {
                                label: '变焦+',
                                value: 'zoom+'
                        },
                        {
                                label: '变焦-',
                                value: 'zoom-'
                        },
                        {
                                label: '画面+',
                                value: 'picture+'
                        },
                        {
                                label: '画面-',
                                value: 'picture-'
                        },
                        {
                                label: '菜单',
                                value: 'menu'
                        },
                        {
                                label: '确认',
                                value: 'ok'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        },
                        {
                                label: '退出',
                                value: 'quit'
                        },
                        {
                                label: '音量+',
                                value: 'vol+'
                        },
                        {
                                label: '音量-',
                                value: 'vol-'
                        },
                        {
                                label: '静音',
                                value: 'mute'
                        },
                        {
                                label: '自动',
                                value: 'auto'
                        },
                        {
                                label: '暂停',
                                value: 'pause'
                        },
                        {
                                label: '亮度',
                                value: 'bright'
                        }
                ]
        },
        {
                devtype: 'ir_hxd_air_purifier',
                arr: [
                        {
                                label: '开关',
                                value: 'onoff'
                        },
                        {
                                label: '自动',
                                value: 'auto'
                        },
                        {
                                label: '风速',
                                value: 'wind'
                        },
                        {
                                label: '定时',
                                value: 'timer'
                        },
                        {
                                label: '模式',
                                value: 'mode'
                        },
                        {
                                label: '负离子',
                                value: 'anion'
                        },
                        {
                                label: '模式(舒适)',
                                value: 'comfort'
                        },
                        {
                                label: '模式(静音)',
                                value: 'mute'
                        },
                        {
                                label: '模式(强劲)',
                                value: 'strong'
                        },
                        {
                                label: '模式(自然)',
                                value: 'nature'
                        },
                        {
                                label: '模式(关闭)',
                                value: 'close'
                        },
                        {
                                label: '睡眠',
                                value: 'sleep'
                        },
                        {
                                label: '智能',
                                value: 'smart'
                        },
                        {
                                label: '关灯',
                                value: 'turn-off-light'
                        },
                        {
                                label: '灯光1',
                                value: 'light1'
                        },
                        {
                                label: '灯光2',
                                value: 'light2'
                        },
                        {
                                label: '灯光3',
                                value: 'light3'
                        },
                        {
                                label: '紫外线',
                                value: 'uv'
                        },
                ]
        },
        {
                devtype: 'ir_hxd_robotcleaner',
                arr: [
                        {
                                label: '开机',
                                value: 'on'
                        },
                        {
                                label: '关机',
                                value: 'off'
                        },
                        {
                                label: '上',
                                value: 'up'
                        },
                        {
                                label: '下',
                                value: 'down'
                        },
                        {
                                label: '左',
                                value: 'left'
                        },
                        {
                                label: '右',
                                value: 'right'
                        },
                        {
                                label: '确定',
                                value: 'ok'
                        },
                        {
                                label: '回充',
                                value: 'charge'
                        },
                        {
                                label: '模式',
                                value: 'mode'
                        },
                        {
                                label: '主页',
                                value: 'main'
                        },
                        {
                                label: '时间',
                                value: 'time'
                        },
                        {
                                label: '区域',
                                value: 'region'
                        },
                        {
                                label: '沿边',
                                value: 'edge'
                        },
                        {
                                label: '局部',
                                value: 'part'
                        },
                        {
                                label: '自动',
                                value: 'auto'
                        },
                        {
                                label: '定点',
                                value: 'fixpoint'
                        },
                        {
                                label: '弓形',
                                value: 'bow'
                        },
                        {
                                label: '杀菌',
                                value: 'wipe'
                        },
                        {
                                label: '预约',
                                value: 'order'
                        },
                        {
                                label: '速度',
                                value: 'speed'
                        },
                        {
                                label: '设置',
                                value: 'set'
                        },
                ]
        },
]

// 背景音乐类cmd
export let class_bgm_cmd = [{
        devtype: 'bgm_customize',
        arr: [
                {
                        label: '前一个',
                        value: 'previous'
                },
                {
                        label: '下一个',
                        value: 'next'
                },
                {
                        label: '单曲循环',
                        value: 'single'
                },
                {
                        label: '顺序播放',
                        value: 'all'
                },
                {
                        label: '音量+',
                        value: 'up'
                },
                {
                        label: '音量-',
                        value: 'down'
                }
        ]
},
{
        devtype: 'bgm_runyo',
        arr: [
                {
                        label: '前一个',
                        value: 'previous'
                },
                {
                        label: '下一个',
                        value: 'next'
                },
                {
                        label: '单曲循环',
                        value: 'single'
                },
                {
                        label: '顺序播放',
                        value: 'all'
                },
                {
                        label: '音量+',
                        value: 'up'
                },
                {
                        label: '音量-',
                        value: 'down'
                }
        ]
},
{
        devtype: 'player',
        arr: [
                {
                        label: '前一页',
                        value: 'pgup'
                },
                {
                        label: '下一页',
                        value: 'pgdn'
                },
                {
                        label: '上',
                        value: 'up'
                },
                {
                        label: '下',
                        value: 'down'
                },
                {
                        label: '左',
                        value: 'left'
                },
                {
                        label: '右',
                        value: 'right'
                },
                {
                        label: '确定',
                        value: 'ok'
                },
                {
                        label: '播放',
                        value: 'play'
                },
                {
                        label: '暂停',
                        value: 'pause'
                },
                {
                        label: '停止',
                        value: 'stop'
                },
                {
                        label: '快进',
                        value: 'forward'
                },
                {
                        label: '快退',
                        value: 'rewind'
                },
                {
                        label: '主页',
                        value: 'home'
                },
                {
                        label: '菜单',
                        value: 'menu'
                },
                {
                        label: '返回',
                        value: 'return'
                },
                {
                        label: '设置',
                        value: 'settings'
                },
                {
                        label: '字幕',
                        value: 'subtitle'
                },
                {
                        label: '音轨',
                        value: 'audio'
                }
        ]
}, {
        devtype: 'stereo',
        arr: [
                {
                        label: 'BD',
                        value: 'bd'
                },
                {
                        label: 'CBL/SAT',
                        value: 'cbl_sat'
                },
                {
                        label: 'GAME',
                        value: 'game'
                },
                {
                        label: 'AUX',
                        value: 'aux'
                }

        ]
}]


// 影音主机情景预设
export let scenpre_device_list = {
        light_list: function () {
                var arr = [];
                for (var i = 0; i < 6; i++) {
                        var obj = {
                                attr: {
                                        onoff: 'open'
                                },
                                vid: "",
                                type: 'relay',
                                scene_type: 'come',
                                name: "",
                                region_id: "",
                                room_id: "",
                        }
                        arr.push(obj);
                }
                return arr;
        }(),
        // 时序器
        sequencer_list: [
                {
                        attr: {},
                        chn_list: [
                                {
                                        passageway: "p1",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p2",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p3",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p4",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p5",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p6",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p7",
                                        scene_type: ""
                                },
                                {
                                        passageway: "p8",
                                        scene_type: ""
                                },
                        ],
                        name: "壹线时序器(通用)",
                        region_id: "",
                        room_id: "",
                        type: "cloud_proto_sequencer",
                        vid: ""
                }
        ],
//        功放
        stereo_list: [
                {
                        attr: {},
                        chn_list: [
                                {
                                        passageway: "passageway_1",
                                        scene_type: ""
                                },
                                {
                                        passageway: "passageway_2",
                                        scene_type: ""
                                }
                        ],
                        vid: "",
                        type: 'cloud_proto_stereo',
                        name: "时序器通用",
                        region_id: "",
                        room_id: "",
                }
        ],

        // 投影机
        projector_list: [{
                attr: {},
                chn_list:[
                        {
                                passageway: "passageway_1",
                                scene_type: ""
                        },
                        {
                                passageway: "passageway_2",
                                scene_type: ""
                        }
                ],
                vid: "",
                type: 'cloud_proto_projector',
                name: "投影机",
                region_id: "",
                room_id: "",
        }],
        player_list:[{
                attr:{},
                vid: "",
                type: 'cloud_proto_player',
                name: "播放器",
                region_id: "",
                room_id: "",
        }]

}



export function setYear() {
        let year = []
        for (var i = 2022; i <= 2036; i++) {
                year.push({
                        label: JSON.stringify(i),
                        value: JSON.stringify(i)
                })
        }
        return year;
}

export function setMonth() {
        let moth = []
        for (var i = 1; i <= 12; i++) {
                var str = i < 10 ? '0' + JSON.stringify(i) : JSON.stringify(i);
                moth.push({
                        label: str,
                        value: JSON.stringify(i)
                })
        }
        return moth;
}
export function setDay() {
        let day = []
        for (var i = 1; i <= 31; i++) {
                var str = i < 10 ? '0' + JSON.stringify(i) : JSON.stringify(i);
                day.push({
                        label: str,
                        value: JSON.stringify(i)
                })
        }
        return day;
}
 