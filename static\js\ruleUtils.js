
var ruleUtils = {
        // 仅限数字和大小写字母,最大字符长度20
        ruleUserName: function (rule, value, callback) {
                if (!value) {
                        return callback(new Error('用户名不能为空'));
                }
                var pattern = /^[a-zA-Z0-9]{1,20}$/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('仅限大小写字母和数字,且最大长度为20'))
                }
        },
        rulePassword: function (rule, value, callback) {
                // console.log(value);
                if (!value) {
                        return callback(new Error('密码不能为空'));
                }
                var pattern = /^[a-zA-Z0-9]{6,20}$/;
                //    var pattern = new RegExp('^[a-zA-Z0-9]{1,20}$'); 
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('仅限大小写字母和数字,且最大长度为6-20'))
                }
        },
        // ip
        ruleIpaddr: function (rule, value, callback) {
                if (!value) {
                        return callback('IP不能为空');
                }
                var pattern = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('不符合IP规则!'))
                }
        },
        ruleInt: function (rule, value, callback) {
                if (!value) {
                        return callback('请输入心跳间隔时间');
                }
                var pattern = /^[0-9]\d*$/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('请输入整数'))
                }
        },
        // 子网掩码
        ruleNetmask: function (rule, value, callback) {
                if (!value) {
                        return callback('子网掩码不能为空');
                }
                var pattern = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;

                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('不符合子网掩码规则'))
                }
        },
        ruleMac: function (rule, value, callback) {

                let reg1 = /^[A-Fa-f0-9]{1,2}-[A-Fa-f0-9]{1,2}-[A-Fa-f0-9]{1,2}-[A-Fa-f0-9]{1,2}-[A-Fa-f0-9]{1,2}-[A-Fa-f0-9]{1,2}$/
                let reg2 = /^[A-Fa-f0-9]{1,2}:[A-Fa-f0-9]{1,2}:[A-Fa-f0-9]{1,2}:[A-Fa-f0-9]{1,2}:[A-Fa-f0-9]{1,2}:[A-Fa-f0-9]{1,2}$/
                if (reg1.test(value)) {
                        return callback();
                } else if (reg2.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('请输入正确的mac地址'))
                }

        },
        // 网关
        ruleGateway: function (rule, value, callback) {
                if (!value) {
                        return callback('网关不能为空');
                }
                var pattern = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;

                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('不符合网关规则'));
                }
        },
        // dns
        ruleDns: function (rule, value, callback) {
                if (!value) {
                        return callback('DNS不能为空');
                }
                var pattern = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('不符合DNS规则'));
                }
        },




        // 验证IP域名
        ruleIP: function (rule, value, callback) {
                if (!value) {
                        return callback(new Error('请输入正确的域名'));
                }
                var pattern = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/
                if (pattern.test(value)) {
                        return callback(pattern.exec(value)[0]);
                } else {
                        // ip不符合
                        return callback(new Error('请正确输入域名,不能使用特殊字符'));
                }
        },

        // 未用到======================

        //邮箱验证
        ruleEmail: function (rule, value, callback) {
                if (!value) {
                        return callback(new Error('请输入邮箱地址'));
                }
                var pattern = /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('请正确输入邮箱字符'));
                }
        },

        //    手机号码
        rulePhoneZn: function (rule, value, callback) {
                if (!value) {
                        return callback(new Error('请输入电话号码'));
                }
                var pattern = /0?(13|14|15|17|18|19)[0-9]{9}$/;
                if (pattern.test(value)) {
                        return callback();
                } else {
                        return callback(new Error('请输入正确的手机号'));
                }
        },

}

module.exports = ruleUtils
