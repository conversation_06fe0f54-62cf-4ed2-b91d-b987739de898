/*

 Vue Router:是vue官方的路由管理器,它和vue.js的核心深度集成,让构建单页面变得易如反掌
 所具备的功能有:
            嵌套的路由视图表
          模块化的,基于组件的路由配置
            路由参数,查询,通配符
          基于vue.js过渡系统的视图过渡效果
          细粒度的导航控制
          带有自动激活的CSS class的链接
          HTML5历史模式或hash模式,在IE9中自动降级
          自定义的滚动条行为

  起步:

    html
      使用router-link组件来导航,通过传入to属性指定链接
          <router-link to=""></router-link>

          路由出口:路由匹配的组件将渲染到这里
          <router-view></router-view>

        javascript
          1. 如果使用模块化机制编程,导入vue和vueRouter,要调用Vue.use(VueRouter);

          2. 定义路由
                  每个路由应该映射一个组件,其中component可以是通过Vue.extend()创建的组件构造器
                  或者,只是一个组件配置对象
                      const routes = [
                                {
                                        path:'/foo',
                                        component:Foo
                                }
                        ]
          3. 创建router实例,然后传'routes'配置
              const router = new Router({
                          routes
                  })
          4. 创建和挂载跟实例
                  记得要通过router配置参数注入路由,从而使整个应用都有路由功能
                  const app = new Vue({
                          router
                  }).$mount('#app');



                  this.$router访问路由器,this.$route访问当前路由

          动态路由匹配
                    动态路径参数,以冒号开头
                    复用组件时,想对路由参数的变化做出相应的话,你可以简单地watch(检测变化) $route 对象
                 watch:{
                         '$route'(to,from){
                                 对路由变化做出响应
                         }
                 }

          *匹配所有路径
          /user-*  会匹配以/user-开头的任意路径

          编程式导航:
             router.push({
                         path:"",
                         name:'',
                         query:{},
                         params:{}
                 })
         如果提供了name,params会忽略
         router.push({ name: 'user', params: { userId: '123' }})
         router.push({ path: 'register', query: { plan: 'private' }})


         router.push(path)和routerrouter.replace(path)区别
         不同: router.replace(path)不会向history添加新纪录,而是替换掉当前的history记录
         this.$router.push(path)({
       path:"/usere/set"
         })
         重定向:redirect

*/

/** 
 *  获取当前域名
       document.main   
       window.location.host
      由于获取到的当前域名不包括 http://，所以把获取到的域名赋给 a 标签的 href 时，别忘了加上 http://，否则单击链接时导航会出错。
 * 
    获取当前url
       
     window.location.hash  
 */