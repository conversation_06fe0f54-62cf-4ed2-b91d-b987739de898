{"烽火(beacon)": [{"chn": 1, "model": "码库1", "code": 0}, {"chn": 2, "model": "码库2", "code": 33}, {"chn": 3, "model": "码库3", "code": 56}, {"chn": 4, "model": "码库4", "code": 61}, {"chn": 5, "model": "码库5", "code": 17}], "华为(Huawei)": [{"chn": 1, "model": "码库1", "code": 100}, {"chn": 2, "model": "码库2", "code": 101}, {"chn": 3, "model": "码库3", "code": 102}, {"chn": 4, "model": "码库4", "code": 103}, {"chn": 5, "model": "码库5", "code": 104}, {"chn": 6, "model": "码库6", "code": 105}, {"chn": 7, "model": "码库7", "code": 106}, {"chn": 8, "model": "码库8", "code": 107}, {"chn": 9, "model": "码库9", "code": 108}, {"chn": 10, "model": "码库10", "code": 50}, {"chn": 11, "model": "码库11", "code": 51}, {"chn": 12, "model": "码库12", "code": 52}, {"chn": 13, "model": "码库13", "code": 53}, {"chn": 14, "model": "码库14", "code": 54}, {"chn": 15, "model": "码库15", "code": 1}, {"chn": 16, "model": "码库16", "code": 2}, {"chn": 17, "model": "码库17", "code": 3}, {"chn": 18, "model": "码库18", "code": 4}, {"chn": 19, "model": "码库19", "code": 5}, {"chn": 20, "model": "码库20", "code": 19}, {"chn": 21, "model": "码库21", "code": 20}], "斯达康(UTS)": [{"chn": 1, "model": "码库1", "code": 6}, {"chn": 2, "model": "码库2", "code": 7}], "同洲(states)": [{"chn": 1, "model": "码库1", "code": 50}, {"chn": 2, "model": "码库2", "code": 51}, {"chn": 3, "model": "码库3", "code": 52}, {"chn": 4, "model": "码库4", "code": 53}, {"chn": 5, "model": "码库5", "code": 54}, {"chn": 6, "model": "码库6", "code": 8}, {"chn": 7, "model": "码库7", "code": 22}, {"chn": 8, "model": "码库8", "code": 23}], "中兴(ZTE)": [{"chn": 1, "model": "码库1", "code": 179}, {"chn": 2, "model": "码库2", "code": 180}, {"chn": 3, "model": "码库3", "code": 181}, {"chn": 4, "model": "码库4", "code": 50}, {"chn": 5, "model": "码库5", "code": 51}, {"chn": 6, "model": "码库6", "code": 52}, {"chn": 7, "model": "码库7", "code": 53}, {"chn": 8, "model": "码库8", "code": 54}, {"chn": 9, "model": "码库9", "code": 9}, {"chn": 10, "model": "码库10", "code": 10}, {"chn": 11, "model": "码库11", "code": 11}, {"chn": 12, "model": "码库12", "code": 24}, {"chn": 13, "model": "码库13", "code": 25}], "大亚(Daya)": [{"chn": 1, "model": "码库1", "code": 12}, {"chn": 2, "model": "码库2", "code": 13}, {"chn": 3, "model": "码库3", "code": 15}], "长虹(Changhong)": [{"chn": 1, "model": "码库1", "code": 89}, {"chn": 2, "model": "码库2", "code": 13}, {"chn": 3, "model": "码库3", "code": 50}, {"chn": 4, "model": "码库4", "code": 51}, {"chn": 5, "model": "码库5", "code": 52}, {"chn": 6, "model": "码库6", "code": 53}, {"chn": 7, "model": "码库7", "code": 54}], "创维(SKYWORTH)": [{"chn": 1, "model": "码库1", "code": 232}, {"chn": 2, "model": "码库2", "code": 93}, {"chn": 3, "model": "码库3", "code": 92}, {"chn": 4, "model": "码库4", "code": 201}, {"chn": 5, "model": "码库5", "code": 90}, {"chn": 6, "model": "码库6", "code": 50}, {"chn": 7, "model": "码库7", "code": 14}, {"chn": 8, "model": "码库8", "code": 51}, {"chn": 9, "model": "码库9", "code": 52}, {"chn": 10, "model": "码库10", "code": 53}, {"chn": 11, "model": "码库11", "code": 54}], "飞利浦(PHILPS)": [{"chn": 1, "model": "码库1", "code": 94}, {"chn": 2, "model": "码库2", "code": 16}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "海尔(Haier)": [{"chn": 1, "model": "码库1", "code": 18}, {"chn": 2, "model": "码库2", "code": 35}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 50}, {"chn": 5, "model": "码库5", "code": 29}], "酷乐视(Coolmusic)": [{"chn": 1, "model": "码库1", "code": 21}, {"chn": 2, "model": "码库2", "code": 35}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 50}, {"chn": 5, "model": "码库5", "code": 29}], "小米(Xiaomi)": [{"chn": 1, "model": "码库1", "code": 186}, {"chn": 2, "model": "码库2", "code": 187}, {"chn": 3, "model": "码库3", "code": 26}, {"chn": 4, "model": "码库4", "code": 27}, {"chn": 5, "model": "码库5", "code": 35}, {"chn": 6, "model": "码库6", "code": 28}, {"chn": 7, "model": "码库7", "code": 50}, {"chn": 8, "model": "码库8", "code": 29}], "三星(Samsung)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 42}, {"chn": 3, "model": "码库3", "code": 43}, {"chn": 4, "model": "码库4", "code": 44}, {"chn": 5, "model": "码库5", "code": 45}, {"chn": 6, "model": "码库6", "code": 46}, {"chn": 7, "model": "码库7", "code": 47}, {"chn": 8, "model": "码库8", "code": 48}], "乐视(Letv)": [{"chn": 1, "model": "码库1", "code": 126}, {"chn": 2, "model": "码库2", "code": 127}, {"chn": 3, "model": "码库3", "code": 128}, {"chn": 4, "model": "码库4", "code": 129}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 51}, {"chn": 7, "model": "码库7", "code": 52}, {"chn": 8, "model": "码库8", "code": 53}, {"chn": 9, "model": "码库9", "code": 54}, {"chn": 10, "model": "码库10", "code": 55}, {"chn": 11, "model": "码库11", "code": 28}, {"chn": 12, "model": "码库12", "code": 29}, {"chn": 13, "model": "码库13", "code": 30}, {"chn": 14, "model": "码库14", "code": 31}, {"chn": 15, "model": "码库15", "code": 32}, {"chn": 16, "model": "码库16", "code": 33}, {"chn": 17, "model": "码库17", "code": 34}, {"chn": 18, "model": "码库18", "code": 28}, {"chn": 19, "model": "码库19", "code": 29}], "忆典(IDER)": [{"chn": 1, "model": "码库1", "code": 61}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 29}, {"chn": 4, "model": "码库4", "code": 170}, {"chn": 5, "model": "码库5", "code": 171}, {"chn": 6, "model": "码库6", "code": 33}, {"chn": 7, "model": "码库7", "code": 34}, {"chn": 8, "model": "码库8", "code": 36}, {"chn": 9, "model": "码库9", "code": 44}], "英菲克(Inphic)": [{"chn": 1, "model": "码库1", "code": 174}, {"chn": 2, "model": "码库2", "code": 175}, {"chn": 3, "model": "码库3", "code": 36}, {"chn": 4, "model": "码库4", "code": 37}, {"chn": 5, "model": "码库5", "code": 28}, {"chn": 6, "model": "码库6", "code": 29}, {"chn": 7, "model": "码库7", "code": 42}, {"chn": 8, "model": "码库8", "code": 55}, {"chn": 9, "model": "码库9", "code": 46}, {"chn": 10, "model": "码库10", "code": 38}, {"chn": 11, "model": "码库11", "code": 59}, {"chn": 12, "model": "码库12", "code": 61}], "海美迪(HIMEDIA)": [{"chn": 1, "model": "码库1", "code": 95}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 39}], "JAV(JAV)": [{"chn": 1, "model": "码库1", "code": 110}, {"chn": 2, "model": "码库2", "code": 40}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}], "杰科(GIEC)": [{"chn": 1, "model": "码库1", "code": 111}, {"chn": 2, "model": "码库2", "code": 42}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}], "开博尔(Kaiboer)": [{"chn": 1, "model": "码库1", "code": 119}, {"chn": 2, "model": "码库2", "code": 120}, {"chn": 3, "model": "码库3", "code": 121}, {"chn": 4, "model": "码库4", "code": 122}, {"chn": 5, "model": "码库5", "code": 123}, {"chn": 6, "model": "码库6", "code": 124}, {"chn": 7, "model": "码库7", "code": 42}, {"chn": 8, "model": "码库8", "code": 43}, {"chn": 9, "model": "码库9", "code": 44}, {"chn": 10, "model": "码库10", "code": 45}, {"chn": 11, "model": "码库11", "code": 46}, {"chn": 12, "model": "码库12", "code": 47}, {"chn": 13, "model": "码库13", "code": 48}, {"chn": 14, "model": "码库14", "code": 28}, {"chn": 15, "model": "码库15", "code": 29}], "天敏(10Moons)": [{"chn": 1, "model": "码库1", "code": 223}, {"chn": 2, "model": "码库2", "code": 224}, {"chn": 3, "model": "码库3", "code": 225}, {"chn": 4, "model": "码库4", "code": 226}, {"chn": 5, "model": "码库5", "code": 158}, {"chn": 6, "model": "码库6", "code": 159}, {"chn": 7, "model": "码库7", "code": 160}, {"chn": 8, "model": "码库8", "code": 161}, {"chn": 9, "model": "码库9", "code": 28}, {"chn": 10, "model": "码库10", "code": 50}, {"chn": 11, "model": "码库11", "code": 29}], "迈乐(Maile)": [{"chn": 1, "model": "码库1", "code": 133}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 56}, {"chn": 4, "model": "码库4", "code": 29}], "美如画(Picture)": [{"chn": 1, "model": "码库1", "code": 134}, {"chn": 2, "model": "码库2", "code": 135}, {"chn": 3, "model": "码库3", "code": 136}, {"chn": 4, "model": "码库4", "code": 137}, {"chn": 5, "model": "码库5", "code": 138}, {"chn": 6, "model": "码库6", "code": 57}, {"chn": 7, "model": "码库7", "code": 28}, {"chn": 8, "model": "码库8", "code": 29}], "我播(WOBO)": [{"chn": 1, "model": "码库1", "code": 227}, {"chn": 2, "model": "码库2", "code": 165}, {"chn": 3, "model": "码库3", "code": 58}, {"chn": 4, "model": "码库4", "code": 59}], "嘉视丽(ScarlettShili)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 62}], "艾普宽带(AIPbroadband)": [{"chn": 1, "model": "码库1", "code": 63}, {"chn": 2, "model": "码库2", "code": 29}], "康佳(Konka)": [{"chn": 1, "model": "码库1", "code": 64}, {"chn": 2, "model": "码库2", "code": 29}], "爱奇艺(IDGArts)": [{"chn": 1, "model": "码库1", "code": 79}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 55}, {"chn": 4, "model": "码库4", "code": 63}, {"chn": 5, "model": "码库5", "code": 23}, {"chn": 6, "model": "码库6", "code": 45}, {"chn": 7, "model": "码库7", "code": 29}], "艾视妻(AIasawife)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 56}, {"chn": 3, "model": "码库3", "code": 29}], "奥林丹顿(Orientalmeal)": [{"chn": 1, "model": "码库1", "code": 80}, {"chn": 2, "model": "码库2", "code": 81}, {"chn": 3, "model": "码库3", "code": 33}, {"chn": 4, "model": "码库4", "code": 38}, {"chn": 5, "model": "码库5", "code": 35}, {"chn": 6, "model": "码库6", "code": 28}, {"chn": 7, "model": "码库7", "code": 50}, {"chn": 8, "model": "码库8", "code": 29}], "边锋(bianfeng)": [{"chn": 1, "model": "码库1", "code": 88}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 29}, {"chn": 4, "model": "码库4", "code": 35}, {"chn": 5, "model": "码库5", "code": 28}, {"chn": 6, "model": "码库6", "code": 50}, {"chn": 7, "model": "码库7", "code": 29}], "冰尊(Icestatue)": [{"chn": 1, "model": "码库1", "code": 58}, {"chn": 2, "model": "码库2", "code": 45}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}], "畅客(ChangKe)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "创维大麦(SKYWORTHbarley)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "第五元素(Thefifthelement)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "迪优美特(Diyomate)": [{"chn": 1, "model": "码库1", "code": 93}, {"chn": 2, "model": "码库2", "code": 38}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}], "锡洛克(ATinLok)": [{"chn": 1, "model": "码库1", "code": 43}, {"chn": 2, "model": "码库2", "code": 41}, {"chn": 3, "model": "码库3", "code": 47}], "Flytosee(Flytosee)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "FireTV(FireTV)": [{"chn": 1, "model": "码库1", "code": 60}, {"chn": 2, "model": "码库2", "code": 61}, {"chn": 3, "model": "码库3", "code": 63}, {"chn": 4, "model": "码库4", "code": 35}, {"chn": 5, "model": "码库5", "code": 28}, {"chn": 6, "model": "码库6", "code": 50}, {"chn": 7, "model": "码库7", "code": 29}], "供视宝(Forspot)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "Goodstar(Goodstar)": [{"chn": 1, "model": "码库1", "code": 56}, {"chn": 2, "model": "码库2", "code": 53}], "海信(Hisense)": [{"chn": 1, "model": "码库1", "code": 204}, {"chn": 2, "model": "码库2", "code": 96}, {"chn": 3, "model": "码库3", "code": 97}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 29}, {"chn": 6, "model": "码库6", "code": 35}, {"chn": 7, "model": "码库7", "code": 28}, {"chn": 8, "model": "码库8", "code": 50}, {"chn": 9, "model": "码库9", "code": 29}], "红富士(RedFuji)": [{"chn": 1, "model": "码库1", "code": 44}, {"chn": 2, "model": "码库2", "code": 47}, {"chn": 3, "model": "码库3", "code": 56}, {"chn": 4, "model": "码库4", "code": 54}, {"chn": 5, "model": "码库5", "code": 55}, {"chn": 6, "model": "码库6", "code": 50}, {"chn": 7, "model": "码库7", "code": 29}], "劲霸王(Jinoverlord)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "精典(Classic)": [{"chn": 1, "model": "码库1", "code": 43}, {"chn": 2, "model": "码库2", "code": 42}], "金亚(JinYa)": [{"chn": 1, "model": "码库1", "code": 115}, {"chn": 2, "model": "码库2", "code": 116}, {"chn": 3, "model": "码库3", "code": 117}, {"chn": 4, "model": "码库4", "code": 38}, {"chn": 5, "model": "码库5", "code": 29}, {"chn": 6, "model": "码库6", "code": 35}, {"chn": 7, "model": "码库7", "code": 48}, {"chn": 8, "model": "码库8", "code": 50}, {"chn": 9, "model": "码库9", "code": 29}], "金运(KimYun)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "科软(Departmentofsoft)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "朗盛泰(LangShengtai)": [{"chn": 1, "model": "码库1", "code": 18}, {"chn": 2, "model": "码库2", "code": 59}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 48}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 39}], "乐光(Musiclight)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "灵云(LingYun)": [{"chn": 1, "model": "码库1", "code": 130}, {"chn": 2, "model": "码库2", "code": 48}, {"chn": 3, "model": "码库3", "code": 59}], "诺清(ThepromiseofQingDynasty)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "欧家美(OuJiamei)": [{"chn": 1, "model": "码库1", "code": 18}, {"chn": 2, "model": "码库2", "code": 59}, {"chn": 3, "model": "码库3", "code": 45}, {"chn": 4, "model": "码库4", "code": 58}, {"chn": 5, "model": "码库5", "code": 60}, {"chn": 6, "model": "码库6", "code": 29}], "贝特斯(Bates)": [{"chn": 1, "model": "码库1", "code": 30}, {"chn": 2, "model": "码库2", "code": 39}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 45}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "PPBOX(PPBOX)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "奇艺宝(Fantasticarttreasure)": [{"chn": 1, "model": "码库1", "code": 48}, {"chn": 2, "model": "码库2", "code": 59}], "瑞珀(Naperville)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 59}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 58}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "百度(Baidu)": [{"chn": 1, "model": "码库1", "code": 82}, {"chn": 2, "model": "码库2", "code": 83}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}, {"chn": 5, "model": "码库5", "code": 35}, {"chn": 6, "model": "码库6", "code": 28}, {"chn": 7, "model": "码库7", "code": 50}, {"chn": 8, "model": "码库8", "code": 29}], "瑞柏(Ribb)": [{"chn": 1, "model": "码库1", "code": 144}, {"chn": 2, "model": "码库2", "code": 145}, {"chn": 3, "model": "码库3", "code": 58}, {"chn": 4, "model": "码库4", "code": 39}], "视美(AstheUnitedStates)": [{"chn": 1, "model": "码库1", "code": 62}, {"chn": 2, "model": "码库2", "code": 49}], "Beans(Beans)": [{"chn": 1, "model": "码库1", "code": 148}, {"chn": 2, "model": "码库2", "code": 149}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 29}, {"chn": 5, "model": "码库5", "code": 35}, {"chn": 6, "model": "码库6", "code": 28}, {"chn": 7, "model": "码库7", "code": 50}, {"chn": 8, "model": "码库8", "code": 29}], "TPmini(TPmini)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 59}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 33}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "拓普赛特(TuopuDorset)": [{"chn": 1, "model": "码库1", "code": 164}, {"chn": 2, "model": "码库2", "code": 28}, {"chn": 3, "model": "码库3", "code": 29}], "完美星空(Perfectstarrysky)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "泰捷(TaiJie)": [{"chn": 1, "model": "码库1", "code": 17}, {"chn": 2, "model": "码库2", "code": 39}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "微星视道(MSIvisualpath)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 36}, {"chn": 4, "model": "码库4", "code": 48}, {"chn": 5, "model": "码库5", "code": 40}, {"chn": 6, "model": "码库6", "code": 33}], "先科(SAST)": [{"chn": 1, "model": "码库1", "code": 230}, {"chn": 2, "model": "码库2", "code": 166}, {"chn": 3, "model": "码库3", "code": 167}, {"chn": 4, "model": "码库4", "code": 168}, {"chn": 5, "model": "码库5", "code": 28}, {"chn": 6, "model": "码库6", "code": 29}], "夏新(Amoi)": [{"chn": 1, "model": "码库1", "code": 169}, {"chn": 2, "model": "码库2", "code": 32}, {"chn": 3, "model": "码库3", "code": 41}], "莹辉(YingHui)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}, {"chn": 3, "model": "码库3", "code": 35}, {"chn": 4, "model": "码库4", "code": 28}, {"chn": 5, "model": "码库5", "code": 50}, {"chn": 6, "model": "码库6", "code": 29}], "沂星(YiXing)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "优德信(OptimalDickson)": [{"chn": 1, "model": "码库1", "code": 28}, {"chn": 2, "model": "码库2", "code": 29}], "广东IPTV(GuangdongIPTV)": [{"chn": 1, "model": "码库1", "code": 50}, {"chn": 2, "model": "码库2", "code": 49}, {"chn": 3, "model": "码库3", "code": 48}], "芒果E族(MangoEfamily)": [{"chn": 1, "model": "码库1", "code": 64}, {"chn": 2, "model": "码库2", "code": 63}], "PPTV(PPTV)": [{"chn": 1, "model": "码库1", "code": 141}, {"chn": 2, "model": "码库2", "code": 142}, {"chn": 3, "model": "码库3", "code": 65}], "大麦盒子(DomyBox)": [{"chn": 1, "model": "码库1", "code": 221}, {"chn": 2, "model": "码库2", "code": 222}, {"chn": 3, "model": "码库3", "code": 91}, {"chn": 4, "model": "码库4", "code": 92}, {"chn": 5, "model": "码库5", "code": 66}], "爱奇艺桔豆(iQIYIjudou)": [{"chn": 1, "model": "码库1", "code": 67}], "百视通(BesTV)": [{"chn": 1, "model": "码库1", "code": 207}, {"chn": 2, "model": "码库2", "code": 84}, {"chn": 3, "model": "码库3", "code": 85}, {"chn": 4, "model": "码库4", "code": 86}, {"chn": 5, "model": "码库5", "code": 68}], "芒果嗨Q(MangoHiQ)": [{"chn": 1, "model": "码库1", "code": 69}], "7V(7V)": [{"chn": 1, "model": "码库1", "code": 70}], "天猫(Tmall)": [{"chn": 1, "model": "码库1", "code": 151}, {"chn": 2, "model": "码库2", "code": 152}, {"chn": 3, "model": "码库3", "code": 153}, {"chn": 4, "model": "码库4", "code": 71}], "亿格瑞(Egreat)": [{"chn": 1, "model": "码库1", "code": 172}, {"chn": 2, "model": "码库2", "code": 173}, {"chn": 3, "model": "码库3", "code": 72}], "优酷(Youku)": [{"chn": 1, "model": "码库1", "code": 73}], "易视(is)": [{"chn": 1, "model": "码库1", "code": 74}], "中国移动(ChinaMobile)": [{"chn": 1, "model": "码库1", "code": 75}, {"chn": 2, "model": "码库2", "code": 76}], "燧石(FlintStone)": [{"chn": 1, "model": "码库1", "code": 147}, {"chn": 2, "model": "码库2", "code": 77}, {"chn": 3, "model": "码库3", "code": 28}, {"chn": 4, "model": "码库4", "code": 49}], "ABC(ABC)": [{"chn": 1, "model": "码库1", "code": 78}], "贝斯特(beisite)": [{"chn": 1, "model": "码库1", "code": 87}], "HDMI(HDMI)": [{"chn": 1, "model": "码库1", "code": 98}], "华硕(ASUS)": [{"chn": 1, "model": "码库1", "code": 99}], "惠科(HKC)": [{"chn": 1, "model": "码库1", "code": 109}], "极米(GIMI)": [{"chn": 1, "model": "码库1", "code": 112}, {"chn": 2, "model": "码库2", "code": 113}, {"chn": 3, "model": "码库3", "code": 114}], "九联(jiulian)": [{"chn": 1, "model": "码库1", "code": 118}], "科朗(Kelang)": [{"chn": 1, "model": "码库1", "code": 125}], "罗技(Logitech)": [{"chn": 1, "model": "码库1", "code": 131}], "MINIX(MINIX)": [{"chn": 1, "model": "码库1", "code": 139}, {"chn": 2, "model": "码库2", "code": 140}], "RCA(RCA)": [{"chn": 1, "model": "码库1", "code": 143}], "Tronsmart(Tronsmart)": [{"chn": 1, "model": "码库1", "code": 146}], "TCL(TCL)": [{"chn": 1, "model": "码库1", "code": 150}], "天猫魔盒(Tmall)": [{"chn": 1, "model": "码库1", "code": 154}, {"chn": 2, "model": "码库2", "code": 155}, {"chn": 3, "model": "码库3", "code": 156}, {"chn": 4, "model": "码库4", "code": 157}], "统帅盒子(iSeebox)": [{"chn": 1, "model": "码库1", "code": 162}, {"chn": 2, "model": "码库2", "code": 163}], "易视宝(iS)": [{"chn": 1, "model": "码库1", "code": 176}], "兆赫(MHz)": [{"chn": 1, "model": "码库1", "code": 177}, {"chn": 2, "model": "码库2", "code": 178}], "联通(Unicom)": [{"chn": 1, "model": "码库1", "code": 182}], "综合(zonghe)": [{"chn": 1, "model": "码库1", "code": 183}, {"chn": 2, "model": "码库2", "code": 184}], "麦格(Maige)": [{"chn": 1, "model": "码库1", "code": 132}, {"chn": 2, "model": "码库2", "code": 185}], "索信(SOSOON)": [{"chn": 1, "model": "码库1", "code": 188}], "米看(mikan)": [{"chn": 1, "model": "码库1", "code": 189}], "摩西(moxi)": [{"chn": 1, "model": "码库1", "code": 190}], "嘉视丽(Koxsni)": [{"chn": 1, "model": "码库1", "code": 191}], "七音符(Greathopes-A)": [{"chn": 1, "model": "码库1", "code": 192}], "爱迪生(edison)": [{"chn": 1, "model": "码库1", "code": 193}], "美纳途(MNATU)": [{"chn": 1, "model": "码库1", "code": 194}], "品胜(PISEN)": [{"chn": 1, "model": "码库1", "code": 195}], "海橙天(Steeigood)": [{"chn": 1, "model": "码库1", "code": 196}], "小霸王(Xiaobawang)": [{"chn": 1, "model": "码库1", "code": 196}], "魔百和(Mobaihe)": [{"chn": 1, "model": "码库1", "code": 234}, {"chn": 2, "model": "码库2", "code": 197}], "SSK飚王(SSK)": [{"chn": 1, "model": "码库1", "code": 198}], "安博(UBOX)": [{"chn": 1, "model": "码库1", "code": 199}], "百视达(MediaPoint)": [{"chn": 1, "model": "码库1", "code": 200}], "莱可(LICO)": [{"chn": 1, "model": "码库1", "code": 202}], "戴利普(DERIPUS)": [{"chn": 1, "model": "码库1", "code": 203}], "诺凯德(HERVE)": [{"chn": 1, "model": "码库1", "code": 205}], "微星视道(MSIDIGTAL)": [{"chn": 1, "model": "码库1", "code": 206}], "小芒果(iMAGO)": [{"chn": 1, "model": "码库1", "code": 208}], "伟皓(wihome)": [{"chn": 1, "model": "码库1", "code": 209}], "电信悦me(yueme)": [{"chn": 1, "model": "码库1", "code": 210}], "智美视(ZIMEDIA)": [{"chn": 1, "model": "码库1", "code": 211}], "蓝旭(Lanxu)": [{"chn": 1, "model": "码库1", "code": 212}], "天尚(Tshifi)": [{"chn": 1, "model": "码库1", "code": 213}], "九猫(JiuMao)": [{"chn": 1, "model": "码库1", "code": 214}], "QQBOX(QQBOX)": [{"chn": 1, "model": "码库1", "code": 215}], "暴风影音(BFBOX)": [{"chn": 1, "model": "码库1", "code": 216}], "塞维特(saiweite)": [{"chn": 1, "model": "码库1", "code": 217}], "普利尔(pulier)": [{"chn": 1, "model": "码库1", "code": 218}], "赛科达(Saikeda)": [{"chn": 1, "model": "码库1", "code": 218}], "美高美(MEIGAOMEI)": [{"chn": 1, "model": "码库1", "code": 219}], "美利多(MANYTEL)": [{"chn": 1, "model": "码库1", "code": 220}], "影能(INENG)": [{"chn": 1, "model": "码库1", "code": 228}], "摩托罗拉(MotorolaVIP)": [{"chn": 1, "model": "码库1", "code": 229}], "悦me(yueme)": [{"chn": 1, "model": "码库1", "code": 231}], "MXQ(MXQ)": [{"chn": 1, "model": "码库1", "code": 233}]}